# NeoX产品版本命名规范

## **执行摘要**

本报告旨在呈报NeoX官方产品版本控制标准。一个严谨、统一的版本控制标准不仅是技术文档，更是贯穿整个软件开发生命周期（SDLC）的核心契约。它通过提供一种通用语言，极大地提升了开发、测试、运维和项目管理团队之间的沟通效率与协作精度。

报告的核心内容涵盖了该标准的四大支柱：统一的命名格式、语义化版本控制（SemVer）的应用、明确的环境标识符以及标准化的应用内包名策略。遵循这些规范将为NeoX带来显著的业务与技术优势，包括但不限于：通过清晰的版本号传达变更影响，从而强化沟通；通过在持续集成/持续交付（CI/CD）流水线中实现自动化门禁，显著提升流程的完整性与安全性；通过隔离不同环境的构建产物，有效降低部署风险；以及通过支持并行安装，极大简化质量保证（QA）流程。

本报告的最终目标，是将此版本控制标准从一套简单的规则，升华为NeoX工程文化的重要基石。通过全面采纳并有效实施该标准，NeoX将能够在其所有技术项目中，系统性地促进效率、清晰度和可靠性，为实现卓越的工程交付奠定坚实基础。

---

## **第一部分：优化后的NeoX版本控制规范（核心交付成果）**

本部分直接响应核心需求，提供一份经过精心优化的、可直接用于项目仓库（例如，作为VERSIONING.md文件）的NeoX版本控制规范。其格式经过优化，旨在实现技术团队内部最大化的可读性与实用性。

### **1.1 引言**

本文档旨在定义NeoX所有软件产品必须遵循的强制性版本命名规范。严格遵守此标准对于在开发、测试和部署过程中保持清晰性、一致性和效率至关重要。

### **1.2 核心命名格式**

所有软件构建产物的命名必须遵循以下格式。此格式为标准化的基础，便于脚本解析和人工识别。

```bash
<ProductName>-<VersionNumber><EnvironmentID>-<ReleaseDate>
```

各组成部分的定义如下：

* **ProductName**: 标识产品或项目的官方唯一名称。  
  * 示例: AppName  
* **VersionNumber**: 遵循MAJOR.MINOR.PATCH格式的语义化版本号。  
  * 示例: 1.2.0  
* **EnvironmentID**: 用于区分不同构建环境的特定标识符。  
  * 有效值: release, staging, testing  
* **ReleaseDate**: 版本发布或构建的日期，采用YYYYMMDD格式。  
  * 示例: 20240903

### **1.3 语义化版本控制（SemVer）指令**

NeoX采纳并强制要求所有项目遵循**语义化版本控制 2.0.0 (Semantic Versioning 2.0.0)** 规范。版本号MAJOR.MINOR.PATCH的递增规则定义如下：

* **主版本号 (MAJOR)**: 当产品发生不兼容的API变更或重大的架构调整时，必须递增主版本号。此时，次版本号和修订号必须归零。  
* **次版本号 (MINOR)**: 当以向后兼容的方式为产品增加新功能时，必须递增次版本号。此时，修订号必须归零。  
* **修订号 (PATCH)**: 当进行向后兼容的错误修复或微小改进时，必须递增修订号。

### **1.4 构建环境标识**

为了清晰地隔离不同阶段的构建产物，标准定义了三个环境标识符。每个标识符对应特定的使用场景和质量门槛。

#### **release 环境**

* **标识符**: release  
* **场景**: 用于正式发布到生产环境的版本，是面向最终用户的高度稳定版本。只有通过所有测试和验收流程的构建才能被标记为此环境。  
* **命名示例**: AppName-1.2.0-release-20240903

#### **staging 环境**

* **标识符**: staging  
* **场景**: 用于在接近生产环境的条件下进行最终测试（如用户验收测试UAT）的版本。此环境的构建通常是生产发布的直接候选版本。  
* **命名示例**: AppName-1.2.0-staging-20240903

#### **testing 环境**

* **标识符**: testing  
* **场景**: 用于开发过程中进行功能测试、集成测试和QA验证的版本。这类构建通常包含新功能或待验证的修复，稳定性不及staging或release版本。  
* **命名示例**: AppName-1.2.0-testing-20240903

### **1.5 应用内包名策略**

为了支持在同一设备（包括移动端和桌面端）上安装来自不同环境的应用程序版本，必须采用环境特定的包名（或安装路径）策略。这极大地便利了测试和验证工作。

以下以Miraimo移动端应用为例，展示此策略的实施方式：

* **Release 环境包名**: jp.co.mkx.miraimo  
* **Staging 环境包名**: jp.co.mkx.miraimo.staging  
* **Testing 环境包名**: jp.co.mkx.miraimo.testing

---

## **第二部分：战略理据与深度分析**

本部分旨在阐释NeoX版本控制规范背后的“为什么”，深入分析其各项规则如何转化为切实的工程优势和战略价值。这些规则并非孤立的技术指令，而是共同构成了一个旨在提升软件交付全流程效率、质量和可预测性的综合性框架。

### **2.1 语义化版本控制：一种精确的技术沟通协议**

该标准强制采用MAJOR.MINOR.PATCH的语义化版本控制方案，并明确了每个部分的递增规则。这远不止是一种简单的编号体系，它更是一种软件组件与其消费者（无论是人类开发者还是自动化系统）之间具有约束力的“契约”。通过版本号本身，就能清晰、无歧义地传达出一次更新的性质和潜在影响。

这种契约的真正威力在于它为自动化依赖管理奠定了坚实的基础。现代软件开发严重依赖于外部和内部的库与服务。当这些依赖项更新时，如何安全地集成它们是一个核心挑战，即所谓的“依赖地狱”。语义化版本控制提供了一个优雅的解决方案。

1. SemVer的规则（MAJOR代表破坏性变更，MINOR代表新功能，PATCH代表修复）是机器可读的。  
2. 包管理工具（如npm, Maven, Cargo, Pip）的核心功能之一就是解析这些版本号。它们可以被配置为自动接受低风险的PATCH更新，或者在开发者同意的情况下接受中等风险的MINOR更新，但对于高风险的MAJOR更新则会强制要求人工审查。  
3. 通过严格遵循NeoX的版本控制标准，开发团队可以自信地配置其自动化工具。这使得安全地、持续地集成依赖项修复和向后兼容的新功能成为可能，而无需耗费大量时间进行手动回归测试。  
4. 最终，这不仅显著减少了集成过程中的手动工作量和意外风险，还通过确保依赖项的更新不会意外地破坏整个应用栈，从而加速了整体的开发周期。版本号不再仅仅是一个标签，而是一个关于风险和影响的可靠信号。

### **2.2 环境门禁与流水线完整性：一种代码化的工作流**

标准中明确定义了testing、staging和release三个环境。这种划分并非随意的，它实际上是对一个结构化、 последовательно的软件晋升流程的固化。代码从初始开发阶段，经过层层质量验证，最终到达生产环境，每一步都由一个明确的环境标识符来标记。

可以将版本名中的环境标识符（testing, staging, release）视为一个软件构建产物在CI/CD流水线中流转的“数字护照”。

1. 一个现代化的CI/CD流水线本质上是一系列自动化的质量门禁，例如单元测试、集成测试、代码扫描、安全审计和性能测试。  
2. 构建产物名称中的环境标识符，如AppName-1.2.0-testing-20240903，清晰地表明了该产物已经通过了哪些门禁，以及它当前所处的质量状态。一个testing构建意味着它仅通过了早期的、面向开发者的测试。而一个staging构建则暗示它已经通过了更严格的、接近生产的验证。  
3. 这种命名约定可以直接用于强制执行流水线规则。例如，部署到生产环境的自动化脚本可以被设计为**只接受**名称中包含-release-标识符的构建产物。这条简单的规则可以从程序上杜绝任何未经充分测试的testing或staging构建被意外部署到生产环境的可能性。在这里，命名本身就成为了保障系统稳定性的一个关键安全机制。

### **2.3 双重视角：结合SemVer与日期戳实现全面可追溯性**

NeoX标准要求版本标识中同时包含语义化版本号（如2.1.0）和发布日期（如20240915）。这种双重组件系统为追踪版本历史提供了两个互补的视角，满足了组织内不同角色的特定需求。

这种设计创造了一种多维度的版本标识符，能够同时服务于技术和业务层面的查询需求。

1. **技术变更视角（服务于开发者与运维工程师）**: 开发者和DevOps/SRE工程师主要关注SemVer。当他们看到版本从2.1.0变为2.2.0时，他们立刻知道有新的向后兼容功能被添加。当版本从2.1.0变为3.0.0时，他们会警觉到存在破坏性变更，需要仔细阅读发布说明并调整相关代码。SemVer清晰地描述了\*\*“什么变了”\*\*。  
2. **时间序列视角（服务于项目经理、支持团队与合规审计）**: 项目经理、客户支持工程师或合规官更关心时间。他们需要知道在某个特定的日期发布了哪个版本，以便将软件交付与项目里程碑对齐，或在调查客户报告的问题时，能够快速定位到问题可能引入的时间点。日期戳清晰地回答了\*\*“何时变更”\*\*。  
3. 通过将两者结合在AppName-2.1.0-release-20240915这样一个原子化的字符串中，NeoX标准消除了歧义。任何团队成员只需看一眼版本名，就能同时获得技术变更和时间两个维度的信息，这极大地减少了跨团队沟通的成本和潜在的误解。

### **2.4 并行安装在质量保证中的战略价值**

标准中规定了为不同环境的构建产物使用不同应用包名的策略（例如，jp.co.mkx.miraimo.staging）。这个看似微小的技术细节，对提升整个测试与反馈循环的效率和效果具有深远的积极影响。

它本质上是在加速人类的反馈循环。

1. 在传统的测试流程中，测试人员（无论是专业的QA工程师还是产品经理）常常需要在他们的设备上卸载当前的生产版本，才能安装一个用于测试的新版本。这个过程不仅耗时，而且非常繁琐，阻碍了快速、频繁的测试。  
2. NeoX的标准通过强制要求为每个环境使用唯一的包名，彻底消除了这一障碍。  
3. 现在，一名QA工程师可以在他们的测试设备上**同时安装**release（生产版）、staging（预发布版）和testing（测试版）三个版本的应用。他们可以轻松地在不同版本的应用之间来回切换，直接比较一个新功能在testing构建中的表现与当前release构建中的行为。  
4. 这种能力带来了连锁效应：更快的问题发现、更精确的错误报告（例如，“这个问题在2.1.0-release版本中是正常的，但在2.2.0-testing版本中出现了”），以及对修复的更快验证。此外，它也极大地降低了非技术背景的利益相关者（如产品经理、设计师）参与测试的门槛，使他们能够轻松地体验和评估新功能，从而提升产品质量并缩短从代码提交到获得有效反馈的时间。

---

## **第三部分：实施、自动化与治理**

一个标准只有在被严格遵守时才能发挥其价值。本部分提供可操作的指导，以确保NeoX版本控制标准能够被有效采纳、自动化执行，并随着时间的推移保持其有效性。

### **3.1 自动化路径：在CI/CD中强制执行标准**

实现标准合规性的最佳途径是将其嵌入到自动化流程中，从而减少人为错误和认知负担。

* **利用CI/CD变量**: 强烈建议在CI/CD流水线（如GitLab CI, GitHub Actions）中，通过组合预定义变量来动态构建版本字符串。这确保了每次构建都自动遵循命名规范。  

```yaml  
# GitLab CI (.gitlab-ci.yml) 示例  
variables:  
  # 假设版本号从Git标签中获取，如 v1.2.3  
  VERSION_NUMBER: ${CI_COMMIT_TAG_MESSAGE}   
  # 日期格式化  
  RELEASE_DATE: $(date +'%Y%m%d')  
script:  
  - |  
    # $BUILD_ENV 在作业中定义为 'testing', 'staging', 或 'release'  
    VERSION_STRING="$PRODUCT_NAME-$VERSION_NUMBER-$BUILD_ENV-$RELEASE_DATE"  
    echo "Building artifact: $VERSION_STRING"
```

* **自动化版本号递增**: 推荐使用如semantic-release这样的工具。该工具可以分析Git提交信息（遵循Conventional Commits规范，如feat:, fix:, BREAKING CHANGE:），自动确定下一个正确的SemVer版本号，创建Git标签，并生成发布说明。这消除了手动决定版本号的猜测和不一致性。  
* **构建脚本集成**: 建议将版本信息作为参数传递给构建脚本（如Android的Gradle, iOS的Fastlane）。这些脚本应被配置为读取CI/CD变量，并自动设置应用程序的版本名（versionName）、版本号（versionCode）和应用ID（applicationId），确保最终生成的APK、IPA或可执行文件完全符合标准。

### **3.2 融入开发者工作流**

版本控制标准不应是开发流程结束时的一个附加步骤，而应被无缝地编织到日常的开发实践中。

* **结合Git分支策略**: 建议采纳与版本类型相匹配的Git分支模型（如GitFlow或其简化版）。分支的命名和生命周期可以与构建环境直接关联：  
  * feature/\*分支的每次提交都可以触发一个testing环境的构建，供开发者和QA进行早期测试。  
  * 当feature/\*分支合并到develop主干时，可以触发一个新的testing构建。  
  * 当准备发布新版本时，从develop创建一个release/2.1.0分支，该分支的构建将自动标记为staging环境，用于UAT。  
  * 最后，当release/2.1.0分支合并到main（或master）并被打上v2.1.0的Git标签时，触发最终的release构建。  
* **使用合并请求（Pull Request）模板**: 推荐在代码仓库中强制使用PR模板。模板应要求开发者明确其变更的性质（新功能、错误修复、破坏性变更）。这不仅有助于代码审查者快速理解变更内容，也为semantic-release等自动化工具提供了必要的信息，以正确地递增版本号。

### **3.3 维护标准的生命力**

业务需求和技术栈是不断演进的。版本控制标准必须具备一个清晰的变更管理流程，以适应这些变化，从而保持其长期有效性。若无主动治理，任何内部标准都将面临“标准衰退”的风险。

随着时间的推移，团队可能会遇到新的场景（例如，需要一个hotfix或demo环境标识符），如果没有正式的渠道来处理这些需求，他们可能会创建非官方的变体，从而导致标准最初旨在解决的不一致性问题再次出现。通过建立正式的治理流程，NeoX可以主动管理这些变更，而非被动应对混乱。

* **指定所有者**: 明确指派一个团队或委员会（例如，“工程卓越”技术委员会或核心DevOps团队）作为该版本控制标准的官方所有者。该所有者负责解释标准、处理变更请求和监督其执行情况。  
* **变更控制流程**: 定义一个清晰的流程来提议对标准的修改。例如，可以在一个专门的Git仓库中通过提交Issue来发起变更讨论。这为所有利益相关者提供了一个公开讨论的平台，并确保任何修改都经过了充分的论证和正式批准。  
* **异常处理框架**: 建立一个处理合理的一次性异常情况的框架。任何偏离标准的行为都应被记录在案，说明其原因和影响范围，并获得相应负责人的批准。这确保了灵活性，同时不会损害标准的整体完整性。

---

## **第四部分：综合快速参考资料**

本部分提供高信息密度的、易于查阅的参考材料，供团队成员在日常工作中使用。

### **4.1 表1：环境规范矩阵**

此表格是一个“速查表”，旨在帮助任何团队成员在几秒钟内理解不同构建产物的用途和命名规则，而无需阅读完整的文档。

| 标识符  | 主要场景                                | 命名示例                       |
| :------ | :-------------------------------------- | :----------------------------- |
| release | 面向最终用户的正式生产版本。            | AppName-2.1.0-release-20240915 |
| staging | 在类生产环境中进行的预发布测试（UAT）。 | AppName-2.1.0-staging-20240910 |
| testing | 开发过程中的内部功能与集成测试。        | AppName-2.1.0-testing-20240905 |

### **4.2 表2：版本号递增决策指南**

此表格将SemVer的抽象规则转化为一个实用的决策工具，旨在消除开发者在确定版本号时的模糊性。它通过将常见的开发活动映射到具体的版本递增操作，来促进一致性并降低遵循标准的认知负荷。

| 如果您的变更涉及...                  | 那么您必须...                                   | 示例             |
| :----------------------------------- | :---------------------------------------------- | :--------------- |
| 引入了不向后兼容的API或架构性变更。  | **递增 MAJOR** 版本号（并将MINOR和PATCH归零）。 | 2.4.5 \-\> 3.0.0 |
| 以向后兼容的方式增加了新功能。       | **递增 MINOR** 版本号（并将PATCH归零）。        | 2.4.5 \-\> 2.5.0 |
| 进行了向后兼容的错误修复或微小改进。 | **递增 PATCH** 版本号。                         | 2.4.5 \-\> 2.4.6 |
