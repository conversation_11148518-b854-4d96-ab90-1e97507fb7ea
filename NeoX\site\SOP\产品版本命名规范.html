
<!doctype html>
<html lang="zh" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Docs site for NeoX.">
      
      
        <meta name="author" content="SongLin Lu">
      
      
      
        <link rel="prev" href="%E6%B5%8B%E8%AF%95%E5%8F%8A%E5%8F%91%E5%B8%83%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html">
      
      
        <link rel="next" href="../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/%E4%BB%A3%E7%A0%81%E9%83%A8%E7%BD%B2.html">
      
      
      <link rel="icon" href="../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.15">
    
    
      
        <title>产品版本命名规范 - NeoX Docs</title>
      
    
    
      <link rel="stylesheet" href="../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
      
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#neox" class="md-skip">
          跳转至
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

<header class="md-header" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="页眉">
    <a href="../index.html" title="NeoX Docs" class="md-header__button md-logo" aria-label="NeoX Docs" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            NeoX Docs
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              产品版本命名规范
            
          </span>
        </div>
      </div>
    </div>
    
      
        <form class="md-header__option" data-md-component="palette">
  
    
    
    
    <input class="md-option" data-md-color-media="" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue"  aria-hidden="true"  type="radio" name="__palette" id="__palette_0">
    
  
</form>
      
    
    
      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
            
<nav class="md-tabs" aria-label="标签" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">
      
        
  
  
  
  
    <li class="md-tabs__item">
      <a href="../index.html" class="md-tabs__link">
        
  
  
    
  
  主页

      </a>
    </li>
  

      
        
  
  
  
    
  
  
    
    
      <li class="md-tabs__item md-tabs__item--active">
        <a href="%E9%A1%B9%E7%9B%AE%E7%A0%94%E5%8F%91%E7%AE%A1%E7%90%86%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html" class="md-tabs__link">
          
  
  
  SOP

        </a>
      </li>
    
  

      
        
  
  
  
  
    
    
      
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/%E4%BB%A3%E7%A0%81%E9%83%A8%E7%BD%B2.html" class="md-tabs__link">
          
  
  
  开发环境搭建

        </a>
      </li>
    
  

    
  

      
        
  
  
  
  
    
    
      
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/%E8%87%AA%E5%8A%A8%E5%8C%96%E5%8F%91%E5%B8%83%E5%B9%B3%E5%8F%B0/Ansible-Semaphore.html" class="md-tabs__link">
          
  
  
  自动化运维

        </a>
      </li>
    
  

    
  

      
        
  
  
  
  
    
    
      
  
  
  
  
    
    
      
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/01-%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html" class="md-tabs__link">
          
  
  
  流程拓扑图

        </a>
      </li>
    
  

    
  

    
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../%E5%85%B3%E4%BA%8E/%E7%89%88%E6%9C%AC%E8%AF%B4%E6%98%8E/release-notes.html" class="md-tabs__link">
          
  
  
  关于

        </a>
      </li>
    
  

      
    </ul>
  </div>
</nav>
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    


  


  

<nav class="md-nav md-nav--primary md-nav--lifted md-nav--integrated" aria-label="导航栏" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../index.html" title="NeoX Docs" class="md-nav__button md-logo" aria-label="NeoX Docs" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    NeoX Docs
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../index.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    主页
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
      
        
        
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_2" checked>
        
          
          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="">
            
  
  
  <span class="md-ellipsis">
    SOP
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            SOP
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="%E9%A1%B9%E7%9B%AE%E7%A0%94%E5%8F%91%E7%AE%A1%E7%90%86%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    项目研发管理流程规范
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="%E6%B5%8B%E8%AF%95%E5%8F%8A%E5%8F%91%E5%B8%83%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    测试及发布流程规范
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    产品版本命名规范
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="%E4%BA%A7%E5%93%81%E7%89%88%E6%9C%AC%E5%91%BD%E5%90%8D%E8%A7%84%E8%8C%83.html" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    产品版本命名规范
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="目录">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      目录
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#_1" class="md-nav__link">
    <span class="md-ellipsis">
      执行摘要
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#neox_1" class="md-nav__link">
    <span class="md-ellipsis">
      第一部分：优化后的NeoX版本控制规范（核心交付成果）
    </span>
  </a>
  
    <nav class="md-nav" aria-label="第一部分：优化后的NeoX版本控制规范（核心交付成果）">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#11" class="md-nav__link">
    <span class="md-ellipsis">
      1.1 引言
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#12" class="md-nav__link">
    <span class="md-ellipsis">
      1.2 核心命名格式
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#13-semver" class="md-nav__link">
    <span class="md-ellipsis">
      1.3 语义化版本控制（SemVer）指令
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#14" class="md-nav__link">
    <span class="md-ellipsis">
      1.4 构建环境标识
    </span>
  </a>
  
    <nav class="md-nav" aria-label="1.4 构建环境标识">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#release" class="md-nav__link">
    <span class="md-ellipsis">
      release 环境
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#staging" class="md-nav__link">
    <span class="md-ellipsis">
      staging 环境
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#testing" class="md-nav__link">
    <span class="md-ellipsis">
      testing 环境
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#15" class="md-nav__link">
    <span class="md-ellipsis">
      1.5 应用内包名策略
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#_2" class="md-nav__link">
    <span class="md-ellipsis">
      第二部分：战略理据与深度分析
    </span>
  </a>
  
    <nav class="md-nav" aria-label="第二部分：战略理据与深度分析">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#21" class="md-nav__link">
    <span class="md-ellipsis">
      2.1 语义化版本控制：一种精确的技术沟通协议
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#22" class="md-nav__link">
    <span class="md-ellipsis">
      2.2 环境门禁与流水线完整性：一种代码化的工作流
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#23-semver" class="md-nav__link">
    <span class="md-ellipsis">
      2.3 双重视角：结合SemVer与日期戳实现全面可追溯性
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#24" class="md-nav__link">
    <span class="md-ellipsis">
      2.4 并行安装在质量保证中的战略价值
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#_3" class="md-nav__link">
    <span class="md-ellipsis">
      第三部分：实施、自动化与治理
    </span>
  </a>
  
    <nav class="md-nav" aria-label="第三部分：实施、自动化与治理">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#31-cicd" class="md-nav__link">
    <span class="md-ellipsis">
      3.1 自动化路径：在CI/CD中强制执行标准
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#32" class="md-nav__link">
    <span class="md-ellipsis">
      3.2 融入开发者工作流
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#33" class="md-nav__link">
    <span class="md-ellipsis">
      3.3 维护标准的生命力
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#_4" class="md-nav__link">
    <span class="md-ellipsis">
      第四部分：综合快速参考资料
    </span>
  </a>
  
    <nav class="md-nav" aria-label="第四部分：综合快速参考资料">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#41-1" class="md-nav__link">
    <span class="md-ellipsis">
      4.1 表1：环境规范矩阵
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#42-2" class="md-nav__link">
    <span class="md-ellipsis">
      4.2 表2：版本号递增决策指南
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    开发环境搭建
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            开发环境搭建
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_3_1" >
        
          
          <label class="md-nav__link" for="__nav_3_1" id="__nav_3_1_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    后端开发
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_3_1_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3_1">
            <span class="md-nav__icon md-icon"></span>
            后端开发
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/%E4%BB%A3%E7%A0%81%E9%83%A8%E7%BD%B2.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    代码部署
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/AWS-ECR%E6%9D%83%E9%99%90%E8%AE%BE%E7%BD%AE.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    AWS ECR权限设置
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/Docker%E7%8E%AF%E5%A2%83%E9%83%A8%E7%BD%B2.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Docker环境部署
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/%E5%90%8E%E7%AB%AF%E4%BB%A3%E7%A0%81%E7%8E%AF%E5%A2%83%E9%85%8D%E7%BD%AE.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    后端代码环境配置
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/FAQ.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    常见问题解答
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    自动化运维
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            自动化运维
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4_1" >
        
          
          <label class="md-nav__link" for="__nav_4_1" id="__nav_4_1_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    自动化发布平台
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_4_1_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4_1">
            <span class="md-nav__icon md-icon"></span>
            自动化发布平台
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/%E8%87%AA%E5%8A%A8%E5%8C%96%E5%8F%91%E5%B8%83%E5%B9%B3%E5%8F%B0/Ansible-Semaphore.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Ansible Semaphore
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    流程拓扑图
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            流程拓扑图
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5_1" >
        
          
          <label class="md-nav__link" for="__nav_5_1" id="__nav_5_1_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    医疗后端
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_5_1_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5_1">
            <span class="md-nav__icon md-icon"></span>
            医疗后端
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5_1_1" >
        
          
          <label class="md-nav__link" for="__nav_5_1_1" id="__nav_5_1_1_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    识别端流程
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="3" aria-labelledby="__nav_5_1_1_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5_1_1">
            <span class="md-nav__icon md-icon"></span>
            识别端流程
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/01-%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    01-生命周期
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/02-%E6%95%B4%E4%BD%93%E6%9E%B6%E6%9E%84.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    02-整体架构
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/03-%E8%AF%86%E5%88%AB%E4%BB%BB%E5%8A%A1%E7%9A%84%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    03-识别任务的生命周期
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/04-%E8%AF%86%E5%88%AB%E4%BB%BB%E5%8A%A1%E7%8A%B6%E6%80%81%E6%B5%81%E8%BD%AC.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    04-识别任务状态流转
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/05-%E7%94%A8%E4%BA%8E%E7%BB%93%E6%9E%9C%E8%BD%AE%E8%AF%A2%E7%9A%84Redis%E7%BC%93%E5%AD%98.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    05-用于结果轮询的Redis缓存
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/06-%E6%9C%8D%E5%8A%A1%E5%88%86%E5%B1%82%E6%9E%B6%E6%9E%84-%E7%BB%84%E4%BB%B6.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    06-服务分层架构-组件
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/07-%E8%AF%86%E5%88%AB%E4%BB%BB%E5%8A%A1task%E4%BF%A1%E6%81%AF%E8%AF%A6%E8%BF%B0.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    07-识别任务task信息详述
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/08-%E5%A4%84%E6%96%B9%E5%90%88%E5%B9%B6%E6%B5%81%E7%A8%8B.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    08-处方合并流程
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/09-FAX%E5%8F%97%E4%BB%98%E6%B5%81%E7%A8%8B.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    09-FAX受付流程
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/NSIPS.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NSIPS
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/GPU%E5%BC%95%E6%93%8E%E8%AF%86%E5%88%AB%E6%B5%81%E7%A8%8B.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    GPU 引擎识别流程
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/Smart-Merge.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Smart Merge
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5_2" >
        
          
          <label class="md-nav__link" for="__nav_5_2" id="__nav_5_2_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    薬師丸賢太
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_5_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5_2">
            <span class="md-nav__icon md-icon"></span>
            薬師丸賢太
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E8%96%AC%E5%B8%AB%E4%B8%B8%E8%B3%A2%E5%A4%AA/%E5%A4%84%E6%96%B9%E7%AC%BA%E4%BF%9D%E5%AD%98%E5%8C%B9%E9%85%8D%E6%B5%81%E7%A8%8B.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    处方笺保存匹配流程
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5_3" >
        
          
          <label class="md-nav__link" for="__nav_5_3" id="__nav_5_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    スマート薬局
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_5_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5_3">
            <span class="md-nav__icon md-icon"></span>
            スマート薬局
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5_3_1" >
        
          
          <label class="md-nav__link" for="__nav_5_3_1" id="__nav_5_3_1_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    薬師丸撫子
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="3" aria-labelledby="__nav_5_3_1_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5_3_1">
            <span class="md-nav__icon md-icon"></span>
            薬師丸撫子
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E3%82%B9%E3%83%9E%E3%83%BC%E3%83%88%E8%96%AC%E5%B1%80/%E8%96%AC%E5%B8%AB%E4%B8%B8%E6%92%AB%E5%AD%90/%E6%89%AB%E6%8F%8F%E4%BB%AA%E8%BF%9E%E6%8E%A5%E7%BB%93%E6%9E%9C%E8%8E%B7%E5%8F%96%E6%B5%81%E7%A8%8B.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    扫描仪连接结果获取流程
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5_3_2" >
        
          
          <label class="md-nav__link" for="__nav_5_3_2" id="__nav_5_3_2_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    通用模块
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="3" aria-labelledby="__nav_5_3_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5_3_2">
            <span class="md-nav__icon md-icon"></span>
            通用模块
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E3%82%B9%E3%83%9E%E3%83%BC%E3%83%88%E8%96%AC%E5%B1%80/%E9%80%9A%E7%94%A8%E6%A8%A1%E5%9D%97/%E6%97%A5%E5%BF%97%E4%B8%8A%E4%BC%A0%E5%AE%A2%E6%88%B7%E7%AB%AF%E6%B5%81%E7%A8%8B.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    日志上传客户端流程
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5_4" >
        
          
          <label class="md-nav__link" for="__nav_5_4" id="__nav_5_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    自动化运维
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_5_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5_4">
            <span class="md-nav__icon md-icon"></span>
            自动化运维
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/medical-backend.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    medical-backend
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/performance.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    performance
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/terraform.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    terraform
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    关于
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            关于
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../%E5%85%B3%E4%BA%8E/%E7%89%88%E6%9C%AC%E8%AF%B4%E6%98%8E/release-notes.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    版本说明
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="neox">NeoX产品版本命名规范<a class="headerlink" href="#neox" title="Permanent link">&para;</a></h1>
<h2 id="_1"><strong>执行摘要</strong><a class="headerlink" href="#_1" title="Permanent link">&para;</a></h2>
<p>本报告旨在呈报NeoX官方产品版本控制标准。一个严谨、统一的版本控制标准不仅是技术文档，更是贯穿整个软件开发生命周期（SDLC）的核心契约。它通过提供一种通用语言，极大地提升了开发、测试、运维和项目管理团队之间的沟通效率与协作精度。</p>
<p>报告的核心内容涵盖了该标准的四大支柱：统一的命名格式、语义化版本控制（SemVer）的应用、明确的环境标识符以及标准化的应用内包名策略。遵循这些规范将为NeoX带来显著的业务与技术优势，包括但不限于：通过清晰的版本号传达变更影响，从而强化沟通；通过在持续集成/持续交付（CI/CD）流水线中实现自动化门禁，显著提升流程的完整性与安全性；通过隔离不同环境的构建产物，有效降低部署风险；以及通过支持并行安装，极大简化质量保证（QA）流程。</p>
<p>本报告的最终目标，是将此版本控制标准从一套简单的规则，升华为NeoX工程文化的重要基石。通过全面采纳并有效实施该标准，NeoX将能够在其所有技术项目中，系统性地促进效率、清晰度和可靠性，为实现卓越的工程交付奠定坚实基础。</p>
<hr />
<h2 id="neox_1"><strong>第一部分：优化后的NeoX版本控制规范（核心交付成果）</strong><a class="headerlink" href="#neox_1" title="Permanent link">&para;</a></h2>
<p>本部分直接响应核心需求，提供一份经过精心优化的、可直接用于项目仓库（例如，作为VERSIONING.md文件）的NeoX版本控制规范。其格式经过优化，旨在实现技术团队内部最大化的可读性与实用性。</p>
<h3 id="11"><strong>1.1 引言</strong><a class="headerlink" href="#11" title="Permanent link">&para;</a></h3>
<p>本文档旨在定义NeoX所有软件产品必须遵循的强制性版本命名规范。严格遵守此标准对于在开发、测试和部署过程中保持清晰性、一致性和效率至关重要。</p>
<h3 id="12"><strong>1.2 核心命名格式</strong><a class="headerlink" href="#12" title="Permanent link">&para;</a></h3>
<p>所有软件构建产物的命名必须遵循以下格式。此格式为标准化的基础，便于脚本解析和人工识别。</p>
<pre><code class="language-bash">&lt;ProductName&gt;-&lt;VersionNumber&gt;&lt;EnvironmentID&gt;-&lt;ReleaseDate&gt;
</code></pre>
<p>各组成部分的定义如下：</p>
<ul>
<li><strong>ProductName</strong>: 标识产品或项目的官方唯一名称。  </li>
<li>示例: AppName  </li>
<li><strong>VersionNumber</strong>: 遵循MAJOR.MINOR.PATCH格式的语义化版本号。  </li>
<li>示例: 1.2.0  </li>
<li><strong>EnvironmentID</strong>: 用于区分不同构建环境的特定标识符。  </li>
<li>有效值: release, staging, testing  </li>
<li><strong>ReleaseDate</strong>: 版本发布或构建的日期，采用YYYYMMDD格式。  </li>
<li>示例: 20240903</li>
</ul>
<h3 id="13-semver"><strong>1.3 语义化版本控制（SemVer）指令</strong><a class="headerlink" href="#13-semver" title="Permanent link">&para;</a></h3>
<p>NeoX采纳并强制要求所有项目遵循<strong>语义化版本控制 2.0.0 (Semantic Versioning 2.0.0)</strong> 规范。版本号MAJOR.MINOR.PATCH的递增规则定义如下：</p>
<ul>
<li><strong>主版本号 (MAJOR)</strong>: 当产品发生不兼容的API变更或重大的架构调整时，必须递增主版本号。此时，次版本号和修订号必须归零。  </li>
<li><strong>次版本号 (MINOR)</strong>: 当以向后兼容的方式为产品增加新功能时，必须递增次版本号。此时，修订号必须归零。  </li>
<li><strong>修订号 (PATCH)</strong>: 当进行向后兼容的错误修复或微小改进时，必须递增修订号。</li>
</ul>
<h3 id="14"><strong>1.4 构建环境标识</strong><a class="headerlink" href="#14" title="Permanent link">&para;</a></h3>
<p>为了清晰地隔离不同阶段的构建产物，标准定义了三个环境标识符。每个标识符对应特定的使用场景和质量门槛。</p>
<h4 id="release"><strong>release 环境</strong><a class="headerlink" href="#release" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>标识符</strong>: release  </li>
<li><strong>场景</strong>: 用于正式发布到生产环境的版本，是面向最终用户的高度稳定版本。只有通过所有测试和验收流程的构建才能被标记为此环境。  </li>
<li><strong>命名示例</strong>: AppName-1.2.0-release-20240903</li>
</ul>
<h4 id="staging"><strong>staging 环境</strong><a class="headerlink" href="#staging" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>标识符</strong>: staging  </li>
<li><strong>场景</strong>: 用于在接近生产环境的条件下进行最终测试（如用户验收测试UAT）的版本。此环境的构建通常是生产发布的直接候选版本。  </li>
<li><strong>命名示例</strong>: AppName-1.2.0-staging-20240903</li>
</ul>
<h4 id="testing"><strong>testing 环境</strong><a class="headerlink" href="#testing" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>标识符</strong>: testing  </li>
<li><strong>场景</strong>: 用于开发过程中进行功能测试、集成测试和QA验证的版本。这类构建通常包含新功能或待验证的修复，稳定性不及staging或release版本。  </li>
<li><strong>命名示例</strong>: AppName-1.2.0-testing-20240903</li>
</ul>
<h3 id="15"><strong>1.5 应用内包名策略</strong><a class="headerlink" href="#15" title="Permanent link">&para;</a></h3>
<p>为了支持在同一设备（包括移动端和桌面端）上安装来自不同环境的应用程序版本，必须采用环境特定的包名（或安装路径）策略。这极大地便利了测试和验证工作。</p>
<p>以下以Miraimo移动端应用为例，展示此策略的实施方式：</p>
<ul>
<li><strong>Release 环境包名</strong>: jp.co.mkx.miraimo  </li>
<li><strong>Staging 环境包名</strong>: jp.co.mkx.miraimo.staging  </li>
<li><strong>Testing 环境包名</strong>: jp.co.mkx.miraimo.testing</li>
</ul>
<hr />
<h2 id="_2"><strong>第二部分：战略理据与深度分析</strong><a class="headerlink" href="#_2" title="Permanent link">&para;</a></h2>
<p>本部分旨在阐释NeoX版本控制规范背后的“为什么”，深入分析其各项规则如何转化为切实的工程优势和战略价值。这些规则并非孤立的技术指令，而是共同构成了一个旨在提升软件交付全流程效率、质量和可预测性的综合性框架。</p>
<h3 id="21"><strong>2.1 语义化版本控制：一种精确的技术沟通协议</strong><a class="headerlink" href="#21" title="Permanent link">&para;</a></h3>
<p>该标准强制采用MAJOR.MINOR.PATCH的语义化版本控制方案，并明确了每个部分的递增规则。这远不止是一种简单的编号体系，它更是一种软件组件与其消费者（无论是人类开发者还是自动化系统）之间具有约束力的“契约”。通过版本号本身，就能清晰、无歧义地传达出一次更新的性质和潜在影响。</p>
<p>这种契约的真正威力在于它为自动化依赖管理奠定了坚实的基础。现代软件开发严重依赖于外部和内部的库与服务。当这些依赖项更新时，如何安全地集成它们是一个核心挑战，即所谓的“依赖地狱”。语义化版本控制提供了一个优雅的解决方案。</p>
<ol>
<li>SemVer的规则（MAJOR代表破坏性变更，MINOR代表新功能，PATCH代表修复）是机器可读的。  </li>
<li>包管理工具（如npm, Maven, Cargo, Pip）的核心功能之一就是解析这些版本号。它们可以被配置为自动接受低风险的PATCH更新，或者在开发者同意的情况下接受中等风险的MINOR更新，但对于高风险的MAJOR更新则会强制要求人工审查。  </li>
<li>通过严格遵循NeoX的版本控制标准，开发团队可以自信地配置其自动化工具。这使得安全地、持续地集成依赖项修复和向后兼容的新功能成为可能，而无需耗费大量时间进行手动回归测试。  </li>
<li>最终，这不仅显著减少了集成过程中的手动工作量和意外风险，还通过确保依赖项的更新不会意外地破坏整个应用栈，从而加速了整体的开发周期。版本号不再仅仅是一个标签，而是一个关于风险和影响的可靠信号。</li>
</ol>
<h3 id="22"><strong>2.2 环境门禁与流水线完整性：一种代码化的工作流</strong><a class="headerlink" href="#22" title="Permanent link">&para;</a></h3>
<p>标准中明确定义了testing、staging和release三个环境。这种划分并非随意的，它实际上是对一个结构化、 последовательно的软件晋升流程的固化。代码从初始开发阶段，经过层层质量验证，最终到达生产环境，每一步都由一个明确的环境标识符来标记。</p>
<p>可以将版本名中的环境标识符（testing, staging, release）视为一个软件构建产物在CI/CD流水线中流转的“数字护照”。</p>
<ol>
<li>一个现代化的CI/CD流水线本质上是一系列自动化的质量门禁，例如单元测试、集成测试、代码扫描、安全审计和性能测试。  </li>
<li>构建产物名称中的环境标识符，如AppName-1.2.0-testing-20240903，清晰地表明了该产物已经通过了哪些门禁，以及它当前所处的质量状态。一个testing构建意味着它仅通过了早期的、面向开发者的测试。而一个staging构建则暗示它已经通过了更严格的、接近生产的验证。  </li>
<li>这种命名约定可以直接用于强制执行流水线规则。例如，部署到生产环境的自动化脚本可以被设计为<strong>只接受</strong>名称中包含-release-标识符的构建产物。这条简单的规则可以从程序上杜绝任何未经充分测试的testing或staging构建被意外部署到生产环境的可能性。在这里，命名本身就成为了保障系统稳定性的一个关键安全机制。</li>
</ol>
<h3 id="23-semver"><strong>2.3 双重视角：结合SemVer与日期戳实现全面可追溯性</strong><a class="headerlink" href="#23-semver" title="Permanent link">&para;</a></h3>
<p>NeoX标准要求版本标识中同时包含语义化版本号（如2.1.0）和发布日期（如20240915）。这种双重组件系统为追踪版本历史提供了两个互补的视角，满足了组织内不同角色的特定需求。</p>
<p>这种设计创造了一种多维度的版本标识符，能够同时服务于技术和业务层面的查询需求。</p>
<ol>
<li><strong>技术变更视角（服务于开发者与运维工程师）</strong>: 开发者和DevOps/SRE工程师主要关注SemVer。当他们看到版本从2.1.0变为2.2.0时，他们立刻知道有新的向后兼容功能被添加。当版本从2.1.0变为3.0.0时，他们会警觉到存在破坏性变更，需要仔细阅读发布说明并调整相关代码。SemVer清晰地描述了**“什么变了”**。  </li>
<li><strong>时间序列视角（服务于项目经理、支持团队与合规审计）</strong>: 项目经理、客户支持工程师或合规官更关心时间。他们需要知道在某个特定的日期发布了哪个版本，以便将软件交付与项目里程碑对齐，或在调查客户报告的问题时，能够快速定位到问题可能引入的时间点。日期戳清晰地回答了**“何时变更”**。  </li>
<li>通过将两者结合在AppName-2.1.0-release-20240915这样一个原子化的字符串中，NeoX标准消除了歧义。任何团队成员只需看一眼版本名，就能同时获得技术变更和时间两个维度的信息，这极大地减少了跨团队沟通的成本和潜在的误解。</li>
</ol>
<h3 id="24"><strong>2.4 并行安装在质量保证中的战略价值</strong><a class="headerlink" href="#24" title="Permanent link">&para;</a></h3>
<p>标准中规定了为不同环境的构建产物使用不同应用包名的策略（例如，jp.co.mkx.miraimo.staging）。这个看似微小的技术细节，对提升整个测试与反馈循环的效率和效果具有深远的积极影响。</p>
<p>它本质上是在加速人类的反馈循环。</p>
<ol>
<li>在传统的测试流程中，测试人员（无论是专业的QA工程师还是产品经理）常常需要在他们的设备上卸载当前的生产版本，才能安装一个用于测试的新版本。这个过程不仅耗时，而且非常繁琐，阻碍了快速、频繁的测试。  </li>
<li>NeoX的标准通过强制要求为每个环境使用唯一的包名，彻底消除了这一障碍。  </li>
<li>现在，一名QA工程师可以在他们的测试设备上<strong>同时安装</strong>release（生产版）、staging（预发布版）和testing（测试版）三个版本的应用。他们可以轻松地在不同版本的应用之间来回切换，直接比较一个新功能在testing构建中的表现与当前release构建中的行为。  </li>
<li>这种能力带来了连锁效应：更快的问题发现、更精确的错误报告（例如，“这个问题在2.1.0-release版本中是正常的，但在2.2.0-testing版本中出现了”），以及对修复的更快验证。此外，它也极大地降低了非技术背景的利益相关者（如产品经理、设计师）参与测试的门槛，使他们能够轻松地体验和评估新功能，从而提升产品质量并缩短从代码提交到获得有效反馈的时间。</li>
</ol>
<hr />
<h2 id="_3"><strong>第三部分：实施、自动化与治理</strong><a class="headerlink" href="#_3" title="Permanent link">&para;</a></h2>
<p>一个标准只有在被严格遵守时才能发挥其价值。本部分提供可操作的指导，以确保NeoX版本控制标准能够被有效采纳、自动化执行，并随着时间的推移保持其有效性。</p>
<h3 id="31-cicd"><strong>3.1 自动化路径：在CI/CD中强制执行标准</strong><a class="headerlink" href="#31-cicd" title="Permanent link">&para;</a></h3>
<p>实现标准合规性的最佳途径是将其嵌入到自动化流程中，从而减少人为错误和认知负担。</p>
<ul>
<li><strong>利用CI/CD变量</strong>: 强烈建议在CI/CD流水线（如GitLab CI, GitHub Actions）中，通过组合预定义变量来动态构建版本字符串。这确保了每次构建都自动遵循命名规范。  </li>
</ul>
<pre><code class="language-yaml"># GitLab CI (.gitlab-ci.yml) 示例  
variables:  
  # 假设版本号从Git标签中获取，如 v1.2.3  
  VERSION_NUMBER: ${CI_COMMIT_TAG_MESSAGE}   
  # 日期格式化  
  RELEASE_DATE: $(date +'%Y%m%d')  
script:  
  - |  
    # $BUILD_ENV 在作业中定义为 'testing', 'staging', 或 'release'  
    VERSION_STRING=&quot;$PRODUCT_NAME-$VERSION_NUMBER-$BUILD_ENV-$RELEASE_DATE&quot;  
    echo &quot;Building artifact: $VERSION_STRING&quot;
</code></pre>
<ul>
<li><strong>自动化版本号递增</strong>: 推荐使用如semantic-release这样的工具。该工具可以分析Git提交信息（遵循Conventional Commits规范，如feat:, fix:, BREAKING CHANGE:），自动确定下一个正确的SemVer版本号，创建Git标签，并生成发布说明。这消除了手动决定版本号的猜测和不一致性。  </li>
<li><strong>构建脚本集成</strong>: 建议将版本信息作为参数传递给构建脚本（如Android的Gradle, iOS的Fastlane）。这些脚本应被配置为读取CI/CD变量，并自动设置应用程序的版本名（versionName）、版本号（versionCode）和应用ID（applicationId），确保最终生成的APK、IPA或可执行文件完全符合标准。</li>
</ul>
<h3 id="32"><strong>3.2 融入开发者工作流</strong><a class="headerlink" href="#32" title="Permanent link">&para;</a></h3>
<p>版本控制标准不应是开发流程结束时的一个附加步骤，而应被无缝地编织到日常的开发实践中。</p>
<ul>
<li><strong>结合Git分支策略</strong>: 建议采纳与版本类型相匹配的Git分支模型（如GitFlow或其简化版）。分支的命名和生命周期可以与构建环境直接关联：  </li>
<li>feature/*分支的每次提交都可以触发一个testing环境的构建，供开发者和QA进行早期测试。  </li>
<li>当feature/*分支合并到develop主干时，可以触发一个新的testing构建。  </li>
<li>当准备发布新版本时，从develop创建一个release/2.1.0分支，该分支的构建将自动标记为staging环境，用于UAT。  </li>
<li>最后，当release/2.1.0分支合并到main（或master）并被打上v2.1.0的Git标签时，触发最终的release构建。  </li>
<li><strong>使用合并请求（Pull Request）模板</strong>: 推荐在代码仓库中强制使用PR模板。模板应要求开发者明确其变更的性质（新功能、错误修复、破坏性变更）。这不仅有助于代码审查者快速理解变更内容，也为semantic-release等自动化工具提供了必要的信息，以正确地递增版本号。</li>
</ul>
<h3 id="33"><strong>3.3 维护标准的生命力</strong><a class="headerlink" href="#33" title="Permanent link">&para;</a></h3>
<p>业务需求和技术栈是不断演进的。版本控制标准必须具备一个清晰的变更管理流程，以适应这些变化，从而保持其长期有效性。若无主动治理，任何内部标准都将面临“标准衰退”的风险。</p>
<p>随着时间的推移，团队可能会遇到新的场景（例如，需要一个hotfix或demo环境标识符），如果没有正式的渠道来处理这些需求，他们可能会创建非官方的变体，从而导致标准最初旨在解决的不一致性问题再次出现。通过建立正式的治理流程，NeoX可以主动管理这些变更，而非被动应对混乱。</p>
<ul>
<li><strong>指定所有者</strong>: 明确指派一个团队或委员会（例如，“工程卓越”技术委员会或核心DevOps团队）作为该版本控制标准的官方所有者。该所有者负责解释标准、处理变更请求和监督其执行情况。  </li>
<li><strong>变更控制流程</strong>: 定义一个清晰的流程来提议对标准的修改。例如，可以在一个专门的Git仓库中通过提交Issue来发起变更讨论。这为所有利益相关者提供了一个公开讨论的平台，并确保任何修改都经过了充分的论证和正式批准。  </li>
<li><strong>异常处理框架</strong>: 建立一个处理合理的一次性异常情况的框架。任何偏离标准的行为都应被记录在案，说明其原因和影响范围，并获得相应负责人的批准。这确保了灵活性，同时不会损害标准的整体完整性。</li>
</ul>
<hr />
<h2 id="_4"><strong>第四部分：综合快速参考资料</strong><a class="headerlink" href="#_4" title="Permanent link">&para;</a></h2>
<p>本部分提供高信息密度的、易于查阅的参考材料，供团队成员在日常工作中使用。</p>
<h3 id="41-1"><strong>4.1 表1：环境规范矩阵</strong><a class="headerlink" href="#41-1" title="Permanent link">&para;</a></h3>
<p>此表格是一个“速查表”，旨在帮助任何团队成员在几秒钟内理解不同构建产物的用途和命名规则，而无需阅读完整的文档。</p>
<table>
<thead>
<tr>
<th style="text-align: left;">标识符</th>
<th style="text-align: left;">主要场景</th>
<th style="text-align: left;">命名示例</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: left;">release</td>
<td style="text-align: left;">面向最终用户的正式生产版本。</td>
<td style="text-align: left;">AppName-2.1.0-release-20240915</td>
</tr>
<tr>
<td style="text-align: left;">staging</td>
<td style="text-align: left;">在类生产环境中进行的预发布测试（UAT）。</td>
<td style="text-align: left;">AppName-2.1.0-staging-20240910</td>
</tr>
<tr>
<td style="text-align: left;">testing</td>
<td style="text-align: left;">开发过程中的内部功能与集成测试。</td>
<td style="text-align: left;">AppName-2.1.0-testing-20240905</td>
</tr>
</tbody>
</table>
<h3 id="42-2"><strong>4.2 表2：版本号递增决策指南</strong><a class="headerlink" href="#42-2" title="Permanent link">&para;</a></h3>
<p>此表格将SemVer的抽象规则转化为一个实用的决策工具，旨在消除开发者在确定版本号时的模糊性。它通过将常见的开发活动映射到具体的版本递增操作，来促进一致性并降低遵循标准的认知负荷。</p>
<table>
<thead>
<tr>
<th style="text-align: left;">如果您的变更涉及...</th>
<th style="text-align: left;">那么您必须...</th>
<th style="text-align: left;">示例</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: left;">引入了不向后兼容的API或架构性变更。</td>
<td style="text-align: left;"><strong>递增 MAJOR</strong> 版本号（并将MINOR和PATCH归零）。</td>
<td style="text-align: left;">2.4.5 -&gt; 3.0.0</td>
</tr>
<tr>
<td style="text-align: left;">以向后兼容的方式增加了新功能。</td>
<td style="text-align: left;"><strong>递增 MINOR</strong> 版本号（并将PATCH归零）。</td>
<td style="text-align: left;">2.4.5 -&gt; 2.5.0</td>
</tr>
<tr>
<td style="text-align: left;">进行了向后兼容的错误修复或微小改进。</td>
<td style="text-align: left;"><strong>递增 PATCH</strong> 版本号。</td>
<td style="text-align: left;">2.4.5 -&gt; 2.4.6</td>
</tr>
</tbody>
</table>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
          <button type="button" class="md-top md-icon" data-md-component="top" hidden>
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  回到页面顶部
</button>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
    <div class="md-copyright__highlight">
      Copyright © 2025 SongLin Lu. All rights reserved.
    </div>
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "..", "features": ["navigation.sections", "navigation.expand", "navigation.tabs", "navigation.top", "navigation.tracking", "toc.follow", "toc.integrate"], "search": "../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "\u5df2\u590d\u5236", "clipboard.copy": "\u590d\u5236", "search.result.more.one": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.more.other": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 # \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.none": "\u6ca1\u6709\u627e\u5230\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.one": "\u627e\u5230 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.other": "# \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.placeholder": "\u952e\u5165\u4ee5\u5f00\u59cb\u641c\u7d22", "search.result.term.missing": "\u7f3a\u5c11", "select.version": "\u9009\u62e9\u5f53\u524d\u7248\u672c"}, "version": null}</script>
    
    
      <script src="../assets/javascripts/bundle.56ea9cef.min.js"></script>
      
    
  </body>
</html>