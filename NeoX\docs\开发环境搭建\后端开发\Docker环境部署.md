# 使用docker镜像部署开发环境框架

```bash
# 进入代码目录
cd medical-docker
# 复制环境变量
cp .env.example .env
# 按需修改COMPOSE_FILE 变量以及对应的yaml文件
# 以事务局为例：
COMPOSE_FILE=docker-compose-bureau.yaml;docker-compose-mongo.yaml;docker-compose-redis.yaml
# 执行构建命令
docker-compose up -d
```

## 环境配置文件示例

`.env` 示例如下：

```ini
# path of code
CODE_PATH=../

# nginx
PRESCRIPTION_NGINX_PORTS_MAP="20201-20210:20201-20210"
BUREAU_NGINX_PORTS_MAP="20301-20310:20301-20310"
POST_OFFICE_NGINX_PORTS_MAP="20401-20410:20401-20410"

# redis
REDIS_IMAGE=redis:6-alpine3.14
REDIS_PORT=26379

# mysql
MYSQL_PORT=23306
MYSQL_ROOT_PASSWORD=123456

# mongo
MONGO_IMAGE=mongo:7.0.15
MONGO_INITDB_ROOT_USERNAME=neox
MONGO_INITDB_ROOT_PASSWORD=123456
MONGO_PORT=37017

# rabbitmq
RABBITMQ_IMAGE=rabbitmq:3.8-management-alpine
RABBITMQ_DEFAULT_USER=neox
RABBITMQ_DEFAULT_PASS=123456
RABBITMQ_WEB_PORT=25672
RABBITMQ_PORT=25673

# LINE license
LINE_SECRET_KEY=
LINE_END_POINT=

# meilisearch
MEILI_MASTER_KEY=a3b1f9c8e7d0a6f5b4c3e2d1f8a7b6c5e4d3c2b1a9f8e7d6c5b4a3f2d1e0c9b8
```

## Docker Compose配置

`docker-compose.yaml` 均在 `medical-docker` 目录下，各文件分别为：

- docker-compose-bureau.yaml

- docker-compose-mongo.yaml

- docker-compose-prescription.yaml

- docker-compose-rabbitmq.yaml

- docker-compose-redis.yaml

- docker-compose-selector.yaml

