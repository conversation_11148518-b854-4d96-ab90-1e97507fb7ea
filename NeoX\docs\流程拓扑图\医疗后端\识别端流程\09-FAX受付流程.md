```mermaid
flowchart TD
    A[PreSentReceptionEvent 事件触发] --> B[handle方法开始]
    B --> C[调用findOldReceptionByPresObj]

    C --> D[调用findNsipsOriginalByPrescription<br/>查找NSIPS原稿数据]
    D --> E{找到NSIPS数据?}

    E -->|否| F[返回null]
    E -->|是| G[调用getReceptionByNsipsId<br/>查找对应受付]

    G --> H[直接通过nsipsId查找受付<br/>条件：merchantId + source=NSIPS + nsipsId]
    H --> I{找到受付?}

    I -->|是| J[返回找到的受付]
    I -->|否| K[查找NsipsComparison记录]

    K --> L{找到NsipsComparison?}
    L -->|否| M[返回null]
    L -->|是| N{prescriptionOriginalId为空?}

    N -->|是| O[返回null]
    N -->|否| P[通过prescriptionOriginalId查找受付]

    P --> Q{找到受付?}
    Q -->|是| R[返回找到的受付]
    Q -->|否| S[返回null]

    J --> T[创建新受付分支]
    R --> T
    S --> U[创建新受付]
    M --> U
    O --> U

    U --> V[调用ReceptionLibrary.createReceptionByNsipsModel]
    V --> W[调用QueueLib.addNumberByReceptionId<br/>标记为复诊REVISIT_FLAG_REVISIT_1]
    W --> X[返回新创建的受付]

    T --> Y[设置oldReceptionId]
    X --> Y
    F --> Z[记录未找到NSIPS数据日志]

    Y --> AA[保存reception对象]
    AA --> BB[查找关联队列号]
    BB --> CC[记录处理成功日志]

    Z --> DD[处理完成]
    CC --> DD

    B --> EE{发生异常?}
    EE -->|是| FF[记录错误日志并重新抛出异常]

    subgraph "NSIPS数据匹配条件"
        GG[患者姓名汉字匹配 OR 患者姓名假名匹配]
        HH[生日匹配]
        II[创建时间匹配]
        JJ[三个条件必须同时满足]
        GG --> JJ
        HH --> JJ
        II --> JJ
    end

    %% 样式类定义
    classDef startEnd fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef process fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef business fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef success fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32
    classDef error fill:#ffcdd2,stroke:#b71c1c,stroke-width:2px,color:#b71c1c
    classDef database fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef external fill:#f5f5f5,stroke:#757575,stroke-width:2px,color:#424242

    %% 应用样式类
    class A startEnd
    class B,C,D,G,H,K,P,V,W,Z,AA,BB,CC process
    class E,I,L,N,Q,EE decision
    class J,R,X success
    class F,M,O,S error
    class U business
    class Y database
    class DD startEnd
    class FF error
    class GG,HH,II,JJ external
```
