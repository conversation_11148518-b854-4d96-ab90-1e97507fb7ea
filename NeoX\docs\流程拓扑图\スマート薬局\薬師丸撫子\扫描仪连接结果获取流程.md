```mermaid
flowchart TD
    %% 主流程
    Start([按下扫描按钮])
    CallWorker([呼叫管理员])
    End([流程结束])
    Start --> Create{调用申请受付接口}
    Create -->|创建失败| CallWorker
    Create -->|创建成功| TryConnectScanner[尝试连接扫描仪]

    subgraph "扫描仪连接处理流程"
        TryConnectScanner --> CheckNativeDirExist{检查本地缓存文件夹是否存在}
        CheckNativeDirExist -->|是| ConnectScanner[连接扫描仪]
        CheckNativeDirExist -->|否| CreateNativeDir[创建本地缓存文件夹]
        CreateNativeDir --> ConnectScanner
        ConnectScanner --> WaitScanResultCallback{等待扫描结果数据超时：最多30s}
        ConnectScanner --> WaitConnectCallback{等待扫描仪连接状态异步回调}
        WaitConnectCallback -->|连接失败| CallWorker
        WaitConnectCallback -->|连接成功| StartScan[通知扫描仪扫描]
        StartScan --> WaitScanCallback{等待扫描仪连接状态异步回调}
        WaitScanCallback -->|扫描失败| CallWorker
        WaitScanCallback -->|扫描成功| ScannerOutputPDF[收到扫描仪输出PDF]

        WaitScanResultCallback -->|超时30s没收到PDF| CallWorker
    end

    subgraph "PDF转图片处理流程"
        ReadPDF[读取PDF文件] --> CutPDFConvertJpeg[切割PDF并转为jpeg数据]
        CutPDFConvertJpeg --> SaveJpegFileList[保存多张图片到本地]
        SaveJpegFileList --> CheckJpegListNotEmpty{检查图片列表是否为空}
        CheckJpegListNotEmpty -->|没有实际图片生成| CallWorker
        CheckJpegListNotEmpty -->|有实际图片生成| SortJpegList[文件排序保证顺序]
    end

    subgraph "QR处理流程-20250728去除"
        QRProcessing["处理QR识别(handleNeoxQR)"] -->
        QRProcessing --> ParseQR["解析QR结果(convertQRToParams)"]
        ParseQR --> CheckValidFiles{检查有效处方文件}
        CheckValidFiles -->|无有效文件| CallWorker
        CheckValidFiles -->|有有效文件| ApplyPosition["占位申请(_apply)"]
    end

    subgraph "文件上传流程"
        SortJpegList --> CheckApplyResult{检查占位结果}
        CheckApplyResult -->|失败| CallWorker
        CheckApplyResult -->|成功| UploadDecision{判断是否需要立即上传}

        UploadDecision -->|需要等待| QueryUpload["轮询上传状态(_queryUpload)"]
        UploadDecision -->|立即上传| UploadFiles["上传所有文件(_uploadAllFile)"]

        UploadFiles --> QueryResult["轮询识别结果(_queryResult)"]
        QueryUpload --> QueryResult
    end

    subgraph "结果处理"
        QueryResult --> CheckNextPage{检查下一页是否需要数据}
        CheckNextPage -->|需要数据| WaitComplete[等待所有流程完成]
        CheckNextPage -->|不需要数据| JumpNext[直接跳转下一页]

        WaitComplete --> FinalCheck{最终检查识别结果}
        FinalCheck -->|成功| ShowResult([显示结果页面])
        FinalCheck -->|失败| CallWorker
    end

    %% 连接关系
    CallWorker --> End
    ShowResult --> End
    JumpNext --> End

    %% 子流程细节
    subgraph "QR识别子流程"
        QRProcessing -->|多页处理| RecognizeMany[识别多页QR码]
        RecognizeMany --> BlankCheck[空白检测]
        BlankCheck --> RotationCheck[旋转检测]
    end

    subgraph "文件上传子流程"
        UploadFiles --> ForEachFile[遍历每个文件]
        ForEachFile --> SingleUpload["单文件上传(_uploadFile)"]
        SingleUpload --> RetryMechanism["重试机制(最多3次)"]
    end

    subgraph "结果查询子流程"
        QueryResult --> TimeoutCheck{检查2分钟超时}
        TimeoutCheck -->|超时| MarkFailed[标记为失败]
        TimeoutCheck -->|未超时| NetworkCheck{检查网络连接}
        NetworkCheck -->|无网络| MarkFailed
        NetworkCheck -->|有网络| APIQuery[调用查询API]
        APIQuery --> ResultAnalysis[分析结果状态]
    end

    %% 样式类定义
    classDef startEnd fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef process fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef error fill:#ffcdd2,stroke:#b71c1c,stroke-width:2px,color:#b71c1c
    classDef success fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32

    %% 应用样式类
    class Start,End,ShowResult startEnd
    class TryConnectScanner,CreateNativeDir,ConnectScanner,StartScan,ScannerOutputPDF,ReadPDF,CutPDFConvertJpeg,SaveJpegFileList,SortJpegList,QRProcessing,ParseQR,ApplyPosition,SetQRData,QueryUpload,UploadFiles,QueryResult,WaitComplete,JumpNext,RecognizeMany,BlankCheck,RotationCheck,ForEachFile,SingleUpload,RetryMechanism,MarkFailed,APIQuery,ResultAnalysis process
    class Create,CheckNativeDirExist,WaitScanResultCallback,WaitConnectCallback,WaitScanCallback,CheckJpegListNotEmpty,CheckValidFiles,CheckApplyResult,UploadDecision,CheckNextPage,FinalCheck,TimeoutCheck,NetworkCheck decision
    class CallWorker error
```
