```mermaid
flowchart TD
    subgraph "Phase 1: 任务创建与上传阶段"
        A1[客户端请求<br/>originFileName, pageCount, receptionId]
        A2[创建Task记录<br/>status: WAIT_UPLOAD<br/>taskId生成]
        A3[图片上传<br/>imagePath, imageList<br/>pretreatment数据]
        A4[状态更新<br/>status: WAIT_RECOGNIZE<br/>processTime.uploaded]
    end

    subgraph "Phase 2: 任务调度阶段"
        B1[Dispatcher扫描<br/>status: WAIT_RECOGNIZE]
        B2[分配资源<br/>qpsId, processId]
        B3[状态更新<br/>status: DISPATCHED]
        B4[写入临时表<br/>p_task_temp]
    end

    subgraph "Phase 3: 识别处理阶段"
        C1[识别进程获取任务<br/>从临时表]
        C2[OCR/QR识别<br/>PrescriptionTrait::identify]
        C3[生成识别结果<br/>presData序列化]
        C4[状态更新<br/>status: RECOGNIZED<br/>processTime.recognized]
    end

    subgraph "Phase 4: 合并判断阶段"
        D1[Merge进程扫描<br/>status: RECOGNIZED]
        D2[合并逻辑判断<br/>姓名、生日、医生匹配]
        D3[合并处方<br/>mergePrescription]
        D4[状态更新<br/>status: MERGED<br/>parentId设置]
    end

    subgraph "Phase 5: 完成入库阶段"
        E1[处方完成<br/>finishPrescription]
        E2[数据入库<br/>prescription_original]
        E3[Redis写入<br/>识别结果缓存]
        E4[通知生成<br/>addNotice]
    end

    subgraph "Phase 6: 客户端获取阶段"
        F1[轮询请求<br/>qrListIds with timestamp]
        F2[Redis查询<br/>按时间戳范围获取]
        F3[返回结果<br/>qrList, merge, finish]
        F4[确认下载<br/>qrNoticeDownloaded]
        F5[Redis清理<br/>deleteRedisTaskByIds]
    end

    %% 流程连接
    A1 --> A2 --> A3 --> A4 --> B1
    B1 --> B2 --> B3 --> B4 --> C1
    C1 --> C2 --> C3 --> C4 --> D1
    D1 --> D2 --> D3 --> D4 --> E1
    E1 --> E2 --> E3 --> E4 --> F1
    F1 --> F2 --> F3 --> F4 --> F5

    %% 样式类定义
    classDef client fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef taskCreate fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef scheduler fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef recognition fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef merge fill:#f1f8e9,stroke:#689f38,stroke-width:2px,color:#33691e
    classDef storage fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32
    classDef cache fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c
    classDef polling fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c

    %% 应用样式类
    class A1 client
    class A2,A3,A4 taskCreate
    class B1,B2,B3,B4 scheduler
    class C1,C2,C3,C4 recognition
    class D1,D2,D3,D4 merge
    class E1,E2,E4 storage
    class E3 cache
    class F1,F2,F3,F4,F5 polling
```
