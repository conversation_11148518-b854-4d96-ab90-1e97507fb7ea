# GPU引擎识别流程

## 全流程版本

```mermaid
flowchart TD
    subgraph "初始处理层"
        A([开始处理])
        B[添加旋转角度]
    end

    subgraph "条件判断层"
        C{是否为离线OCR用户<br/>且GPU服务器可用?}
        D{判断客户端类型<br/>是否为API发送}
        R{ocrResult是否<br/>为空?}
    end

    subgraph "API处理层"
        E[请求API识别_全流程]
        F[记录API识别结果]
        J[请求API识别_OCR]
        O[记录OCR识别结果]
    end

    subgraph "结果处理层"
        G{结果中是否有<br/>qrContent?}
        H[使用JahisParser解析QR内容]
        I{是否成功生成<br/>处方?}
        K[设置处方属性<br/>endFlag=END<br/>qrFlag=YES<br/>isGPU=1]
        P{OCR结果是否<br/>为空?}
        M[获取任务的ocrResult]
    end

    subgraph "业务处理层"
        N[处理离线处方]
        Q[处理普通处方识别]
        S[离线处方处理流程]
        T[普通处方处理流程]
    end

    subgraph "输出层"
        L([返回QR处方结果])
        U([返回离线处方结果])
        V([返回普通处方结果])
    end

    %% 流程连接关系
    A --> B
    B --> C

    C -->|是| D
    C -->|否| M

    D -->|是 API发送| E
    D -->|否 非API发送| J

    E --> F
    F --> G

    G -->|有| H
    G -->|无| N

    H --> I

    I -->|是| K
    I -->|否| N

    K --> L

    J --> O
    O --> P

    P -->|不为空| N
    P -->|为空| Q

    M --> R
    R -->|不为空| N
    R -->|为空| Q

    N --> S
    Q --> T

    S --> U
    T --> V

    %% 样式类定义
    classDef startEnd fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef process fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef api fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef business fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef output fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32

    %% 应用样式类
    class A,L,U,V startEnd
    class B,H,K,M process
    class C,D,G,I,P,R decision
    class E,F,J,O api
    class N,Q,S,T business
```

## 简化版本

```mermaid
flowchart TD
    subgraph "图片预处理阶段"
        A([输入图片]) --> B[加载图片]
        B --> C{分类模型过滤?}
        C -->|是| D[过滤无关图片]
        C -->|否| G[继续处理]
        D --> E{是否需要旋转?}
        E -->|是| F[旋转图片]
        E -->|否| G
        F --> G
    end

    subgraph "AI识别处理阶段"
        G --> H[分割模型处理]
        H --> I[结构检测模型处理]
        I --> J{是否识别QR码?}
        J -->|是| K[解码QR码]
        J -->|否| L[处方结构处理<br/>头部/处方/尾部]
        K --> L
    end

    subgraph "结果处理阶段"
        L --> M[OCR识别]
        M --> N[后处理与结果合并]
        N --> O([输出结果])
    end

    %% 样式类定义
    classDef startEnd fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef process fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef aiProcess fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef output fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32

    %% 应用样式类
    class A,O startEnd
    class B,D,F,G,K,L,M,N process
    class C,E,J decision
    class H,I aiProcess
```
