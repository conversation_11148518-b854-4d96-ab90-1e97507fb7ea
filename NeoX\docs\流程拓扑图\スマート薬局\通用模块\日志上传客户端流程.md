```mermaid
flowchart TD
    %% 主流程
    Start([已登录状态，进入App时服务启动])
    Start --> onInit["调用 onInit()"]
    onInit --> onReady["调用 onReady()"]
    onReady --> Delay10s[延迟 10 秒]
    Delay10s --> LoopStart{开始循环拉取系统信息}

    %% 系统信息轮询循环
    subgraph "系统信息轮询循环"
        LoopStart --> CallSystem[调用 system 接口]
        CallSystem --> CheckError{接口调用是否出错?}
        CheckError -->|是| LogError1[记录错误并保存日志]
        LogError1 --> DelayLoop1["延迟 _systemLoopDelayMs 毫秒 默认一分钟"]
        DelayLoop1 --> LoopStart
        CheckError -->|否| ProcessSystem[解析接口返回 SystemModel]
        ProcessSystem --> UpdateDelay["更新 _systemLoopDelayMs"]
        ProcessSystem --> GetDateList["获取 model.logDate 列表"]
        GetDateList --> CheckEmpty{日期列表是否为空?}
        CheckEmpty -->|是| DelayLoop2["延迟 _systemLoopDelayMs 毫秒"]
        DelayLoop2 --> LoopStart
        CheckEmpty -->|否| DeleteZips[删除所有缓存 Zip 文件]
        DeleteZips --> ZipLogToServer["调用 _zipLogToServer(dateList)"]
        ZipLogToServer --> DelayLoop3["延迟 _systemLoopDelayMs 毫秒"]
        DelayLoop3 --> LoopStart
    end

    %% 压缩并上传日志子流程
    subgraph "_zipLogToServer(dateList)"
        ZipLogToServer --> ForEachDate[遍历每个日期 date]
        ForEachDate --> CheckDateEmpty{date 是否为空?}
        CheckDateEmpty -->|是| NextDate1[跳到下一个日期]
        NextDate1 --> ForEachDate

        CheckDateEmpty -->|否| GetLogPaths[通过 nativeChannel 获取日志路径列表]
        GetLogPaths --> PrepareZipDir["检查并创建 $filePath/logZip 目录"]
        PrepareZipDir --> CollectFiles[组装 File 对象列表]
        CollectFiles --> CheckFilesEmpty{文件列表是否为空?}
        CheckFilesEmpty -->|是| LogError2[记录 文件不存在 错误]
        LogError2 --> UploadStatus_FileNotExist[上报状态 3：文件不存在]
        UploadStatus_FileNotExist --> NextDate2[跳到下一个日期]
        NextDate2 --> ForEachDate

        CheckFilesEmpty -->|否| ZipFiles["调用 _zipFiles(files, zipPath)"]
        ZipFiles --> CheckZipExist{Zip 文件是否存在?}
        CheckZipExist -->|否| LogError3[记录 Zip 不存在 错误]
        LogError3 --> UploadStatus_ZipNotExist[上报状态 3：文件不存在]
        UploadStatus_ZipNotExist --> NextDate3[跳到下一个日期]
        NextDate3 --> ForEachDate

        CheckZipExist -->|是| UploadZip["调用 _uploadLogZip(zipName, date, file)"]
        UploadZip --> ReceiveResult[返回 uploadStr]
        ReceiveResult --> CheckUploadSuccess{uploadStr == success?}
        CheckUploadSuccess -->|是| LogSuccess[记录上传成功]
        LogSuccess --> UploadStatus_Success[上报状态 2：上传成功]
        UploadStatus_Success --> NextDate4[下一个日期]
        CheckUploadSuccess -->|否| UploadStatus_Failed[上报状态 4：上传失败，附带错误信息]
        UploadStatus_Failed --> NextDate4
        NextDate4 --> ForEachDate
    end

    %% 样式类定义
    classDef startEnd fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef process fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef error fill:#ffcdd2,stroke:#b71c1c,stroke-width:2px,color:#b71c1c
    classDef success fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32

    %% 应用样式类
    class Start startEnd
    class onInit,onReady,Delay10s,CallSystem,ProcessSystem,UpdateDelay,GetDateList,DelayLoop1,DelayLoop2,DeleteZips,ZipLogToServer,DelayLoop3,ForEachDate,NextDate1,GetLogPaths,PrepareZipDir,CollectFiles,ZipFiles,ReceiveResult,NextDate2,NextDate3,NextDate4 process
    class LoopStart,CheckError,CheckEmpty,CheckDateEmpty,CheckFilesEmpty,CheckZipExist,CheckUploadSuccess decision
    class LogError1,LogError2,LogError3,UploadStatus_FileNotExist,UploadStatus_ZipNotExist,UploadStatus_Failed error
    class LogSuccess,UploadStatus_Success success
```
