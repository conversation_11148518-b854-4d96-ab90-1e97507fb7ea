# 处方识别项目后端代码的环境配置

## 通过docker命令进入镜像

```bash
docker run -it -v {neox-med-backend路径}:/app {镜像名} bash
```

## 创建.env环境变量

```ini
APP_NAME=NeoX-Med
APP_ENV=staging
APP_KEY=base64:GZCdO0O9WFqDCwOatkHtbC4m2j0PKX+DHCGd4YPSu64=
APP_DEBUG=false
APP_URL=https://medical-api-dev.moair.net
DOMAIN_NAME=https://medical-api-dev.moair.net

LOG_CHANNEL=daily

#DB_CONNECTION=mysql
DB_HOST=***********
DB_PORT=33306
DB_DATABASE=medical_merchant
DB_USERNAME=root
DB_PASSWORD=123456

# MongoDB配置
MONGODB_HOST=***********
MONGODB_PORT=47017
MONGODB_DATABASE=neox-ocr-zwr
MONGODB_USERNAME=neox
MONGODB_PASSWORD=123456

# Redis配置
REDIS_HOST=dev-redis
REDIS_PASSWORD=
REDIS_PORT=6379

# JWT配置
JWT_SECRET=84ynFPhr5D5b4YPoPXvXf6reTt6fWowpuReEhQF0gSMWCpn3vBdGHZVxssViVqJE
JWT_PUBLIC_KEY=file:///data/www/neox-med-backend/storage/oauth-public.key
JWT_PRIVATE_KEY=file:///data/www/neox-med-backend/storage/oauth-private.key
JWT_ALGO=RS256
JWT_TTL=5256000
JWT_REFRESH_TTL=5256000

# AWS S3配置
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=M8z3KsfSYR+Pj9JU/mB9uOll7O9IFauyFR9Bow8m
AWS_DEFAULT_REGION=ap-northeast-1
AWS_BUCKET=yakumaru-development-a86ez6twf9d1sbj4a39t9uicubp7sapn1a-s3alias

# 时区设置
TIMEZONE=Asia/Tokyo
```

## 安装composer依赖

```bash
#在docker容器内部
cd /data/www/neox-med-backend
composer install
```

## 配置完毕后验证

```bash
# 在docker容器内部
# 请将{test.jpeg}部分，替换为容器内部可以访问到的宿主机文件的映射路径
cd /data/www/neox-med-backend
php artisan single:validation:prescription 3500 92 {test.jpeg}
```

**如果看到类似如下结果，说明处方识别逻辑流程在本地初步打通：**

```
array:5 [
  "patient" => array:15 [
    "nameKanji" => array:2 [
      "name" => "患者名"
      "value" => "二本柳 初恵"
    ］
    "nameKana" => array:2 [
      "name" => "患者名（フリガナ）"
      "value" => "ニホンヤナギハツエ"
    ］
    "birthday" => array:2 [
      "name" => "患者生年月日"
      "value" => "19651108"
    ］
    // ... 更多输出
  ]
  // ... 更多数据
]
```

> 按照如上配置的情况下，一些关键服务会连接远程测试环境服务，例如：MongoDB 和 RabbitMQ。 