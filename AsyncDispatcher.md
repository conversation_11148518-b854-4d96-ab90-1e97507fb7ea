graph TD
  START["运行命令：prescriptionAsyncDispatcher"] --> INIT["加载配置<br/>- dispatchInterval 轮询间隔<br/>- dispatchQpsLockTtl（保留但不再用于锁QPS）"]
  INIT --> LOOP{{"循环（永不退出）"}}

  LOOP --> B["获取空闲进程列表 getIdleProcessList（Mongo process_pool）<br/>- status = IDLE<br/>- updatedAt > 当前-1秒<br/>- 排除终止中的实例（Redis: bureauTerminatingInstanceName）<br/>- hostname 去重，返回全部空闲进程"]
  B --> C["获取排队任务 getQueueingTaskList（Mongo ptask）<br/>- status = WAIT_RECOGNIZE<br/>- _id > 当前-5分钟<br/>- 若 USE_GENERIC_OCR_FOR_ENQUETE=true 则仅取处方<br/>- 按 timestamp 排序，返回全部符合条件"]
  C --> D{{"是否存在排队任务？"}}

  D -- "是" --> E["执行分配 doDispatching<br/>遍历任务，依次取空闲进程hostname（array_shift）<br/>若取到进程则记录 {'t': 任务ID, 'p': 进程}，直到耗尽"]
  E --> G["写入任务处理信息 setHandlingDetailToTask<br/>- 校验键 't' 与 'p' 存在
- 将任务 status → DISPATCHED
- 保存到临时任务表 saveToTempTaskTable(进程, 任务)
- 异常则转失败 transferToFailedById"]
  G --> H["记录调度日志 [DISPATCHER STATUS]<br/>- process_cnt_start<br/>- task_cnt_dispatched"]
  H --> ORPH["处理孤儿任务 handleOrphanTask"]

  D -- "否" --> SLEEP["休眠 dispatchInterval（微秒）"]
  SLEEP --> ORPH

  ORPH --> ORPH_TICK{{"是否到 10 秒周期？"}}
  ORPH_TICK -- "是" --> OQ["查找候选孤儿任务 getOrphanTask(limit=10)<br/>- status = DISPATCHED<br/>- _id > 当前-10分钟<br/>- 取字段：_id, updatedAt"]
  OQ --> OCHK{{"是否孤儿？<br/>当前时间 - updatedAt > 5 秒"}}
  OCHK -- "是" --> OU["将任务置回 WAIT_RECOGNIZE<br/>记录日志『Orphan Task Handled』"]
  OU --> LOOP
  OCHK -- "否" --> LOOP
  ORPH_TICK -- "否" --> LOOP
