```mermaid
flowchart TD
    subgraph "初始化阶段"
        StartDispatcher([运行命令：prescriptionAsyncDispatcher])
        InitConfig[加载配置<br/>- dispatchInterval 轮询间隔<br/>- dispatchQpsLockTtl（保留但不再用于锁QPS）]
        MainLoop{循环（永不退出）}
    end

    subgraph "数据获取阶段"
        GetIdleProcesses[(获取空闲进程列表 getIdleProcessList（Mongo process_pool）<br/>- status = IDLE<br/>- updatedAt > 当前-1秒<br/>- 排除终止中的实例（Redis: bureauTerminatingInstanceName）<br/>- hostname 去重，返回全部空闲进程)]
        GetQueueingTasks[(获取排队任务 getQueueingTaskList（Mongo ptask）<br/>- status = WAIT_RECOGNIZE<br/>- _id > 当前-5分钟<br/>- 若 USE_GENERIC_OCR_FOR_ENQUETE=true 则仅取处方<br/>- 按 timestamp 排序，返回全部符合条件)]
        HasTasks{是否存在排队任务？}
    end

    subgraph "任务调度阶段"
        DoDispatching[执行分配 doDispatching<br/>遍历任务，依次取空闲进程hostname（array_shift）<br/>若取到进程则记录 &#123;'t': 任务ID, 'p': 进程&#125;，直到耗尽]
        SetHandlingDetail[写入任务处理信息 setHandlingDetailToTask<br/>- 校验键 't' 与 'p' 存在<br/>- 将任务 status → DISPATCHED<br/>- 保存到临时任务表 saveToTempTaskTable(进程, 任务)<br/>- 异常则转失败 transferToFailedById]
        LogDispatchStatus[记录调度日志 [DISPATCHER STATUS]<br/>- process_cnt_start<br/>- task_cnt_dispatched]
        SleepInterval[休眠 dispatchInterval（微秒）]
    end

    subgraph "孤儿任务处理阶段"
        HandleOrphanTask[处理孤儿任务 handleOrphanTask]
        OrphanTick{是否到 10 秒周期？}
        GetOrphanTasks[(查找候选孤儿任务 getOrphanTask(limit=10)<br/>- status = DISPATCHED<br/>- _id > 当前-10分钟<br/>- 取字段：_id, updatedAt)]
        CheckOrphan{是否孤儿？<br/>当前时间 - updatedAt > 5 秒}
        UpdateOrphanTask[将任务置回 WAIT_RECOGNIZE<br/>记录日志『Orphan Task Handled』]
    end

    %% 连接关系
    StartDispatcher --> InitConfig
    InitConfig --> MainLoop
    MainLoop --> GetIdleProcesses
    GetIdleProcesses --> GetQueueingTasks
    GetQueueingTasks --> HasTasks

    HasTasks -->|是| DoDispatching
    DoDispatching --> SetHandlingDetail
    SetHandlingDetail --> LogDispatchStatus
    LogDispatchStatus --> HandleOrphanTask

    HasTasks -->|否| SleepInterval
    SleepInterval --> HandleOrphanTask

    HandleOrphanTask --> OrphanTick
    OrphanTick -->|是| GetOrphanTasks
    GetOrphanTasks --> CheckOrphan
    CheckOrphan -->|是| UpdateOrphanTask
    UpdateOrphanTask --> MainLoop
    CheckOrphan -->|否| MainLoop
    OrphanTick -->|否| MainLoop

    %% 样式类定义
    classDef startEnd fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef process fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef database fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef loop fill:#e1f5fe,stroke:#0277bd,stroke-width:2px,color:#01579b
    classDef sleep fill:#fce4ec,stroke:#ad1457,stroke-width:2px,color:#880e4f

    %% 应用样式类
    class StartDispatcher startEnd
    class InitConfig,DoDispatching,SetHandlingDetail,LogDispatchStatus,UpdateOrphanTask process
    class HasTasks,OrphanTick,CheckOrphan decision
    class GetIdleProcesses,GetQueueingTasks,GetOrphanTasks database
    class MainLoop,HandleOrphanTask loop
    class SleepInterval sleep
```
