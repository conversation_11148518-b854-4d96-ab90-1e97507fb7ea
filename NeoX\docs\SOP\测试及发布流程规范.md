# 测试及发布流程规范

## **第一部分：基础框架**

本章节旨在建立流程的理论基础，通过阐述核心目标、指导原则、适用范围及关键术语，确保所有团队成员对流程有统一、明确的理解，并在此共识上协同工作。

### **1.1 总体目标**

本标准作业程序（SOP）旨在为公司所有软件产品的测试与发布活动，建立一个系统化、可预测且高质量的管理流程。其核心目标是通过严谨的分析、验证与确认手段，评估产品的质量与可靠性，主动识别并缓解潜在风险，最终确保每一次部署到生产环境的变更都能增强系统稳定性，并为客户创造价值。原则上，任何未经本SOP规定流程测试的程序或产品，均不得发布至生产环境。

### **1.2 指导原则**

本SOP的有效执行依赖于以下核心原则，它们是贯穿整个流程的文化与行为准则：

* **质量共担原则 (Quality as a Shared Responsibility)**：质量保证（QA）团队负责管控和实施测试流程，但构建高质量的产品是产品、研发、测试及运维团队的共同责任。从需求定义到上线维护，每个环节的参与者都需为产品的最终质量负责。  
* **记录即沟通原则 (Communication via Record)**：所有关键的沟通、决策、确认及变更，都必须在Jira等指定的项目管理工具中留下明确的文本记录。口头协议、即时通讯工具中的讨论不能作为流程推进的依据。此原则确保了所有操作的可追溯性、透明度和问责性。  
* **质量左移原则 (Shift-Left Quality)**：鼓励在开发生命周期的早期阶段就注入质量保障措施。这不仅是QA团队的职责，更是研发团队的份内工作。要求研发人员进行详尽的自测并提供报告、提前准备回滚方案等，都是此原则的具体体现。其目的是尽早发现和修复问题，降低后期修复成本和风险。  
* **风险驱动的发布节奏原则 (Risk-Based Release Cadence)**：所有发布活动的时间安排，尤其是针对重要产品设置的发布截止时间（如北京时间17:30），其根本目的是为了最小化对客户业务的潜在影响，并确保在发布后有充足的人力进行监控和应对可能发生的突发事件。  
* **可验证的审计追踪原则 (Verifiable Audit Trail)**：强制要求在Jira中建立从开发任务票、到QA测试票、再到上线票的清晰链接关系。这构建了一条完整的审计链，对于问题排查、责任界定、流程优化以及事后复盘至关重要。

### **1.3 适用范围与例外**

本SOP适用于公司内所有产品线的内部测试与发布工作，涵盖了新功能开发、缺陷修复、配置变更等所有旨在部署到共享测试环境或生产环境的软件变更。

任何对本流程的偏离都必须遵循既定的例外处理机制。一个明确的例外是，部分特殊需求（如仅涉及算法精度调优）可由研发团队在自测后自行发布。然而，此类活动也必须在Jira中创建任务并进行完整记录，其具体操作规程详见本文档附录。

### **1.4 术语表**

为确保沟通的精确性，以下关键术语在本SOP中具有特定含义：

* **重要产品 (Important Product)**：指因其对客户收入有直接影响、拥有大量活跃用户，或作为其他重要产品的关键上游依赖而被官方指定为一级（Tier 1）的任何产品或服务。例如，文档中提及的“贤太”即为此类产品。重要产品的清单由技术委员会或产品领导团队维护，并有明确的文档记录。  
* **自测报告 (Self-Test Report)**：由研发人员编写，用以证明其开发的功能在开发环境中按预期工作的证据集合。形式可以是截图、屏幕录像、日志文件、单元测试结果或Confluence页面链接等，但必须包含明确的自测完成时间信息。  
* **回滚手顺 (Rollback Procedure)**：一份详细、可执行的技术文档，指导运维人员在必要时能够安全、快速地将已部署的变更从生产环境撤销，使系统恢复至发布前的稳定状态。在可行的情况下，回滚方案本身也应经过测试。  
* **代码冻结 (Code Freeze)**：指在发布周期中的一个特定时间点，在此之后，除了修复严重影响发布的关键缺陷（Release Blocker）外，任何新的功能性或非关键性代码变更都不得合入发布分支。  
* **职责分离 (Separation of Duties)**：一项核心的风险控制策略，要求执行生产环境部署的人员（上线人员）不得是该功能点的主要研发人员。此举旨在通过引入独立的审查视角来降低部署风险。

## **第二部分：角色与职责矩阵 (RACI)**

为明确各角色在软件测试与发布流程中的具体职责，消除模糊地带，特制定以下RACI矩阵。RACI是项目管理中的一种工具，用于定义谁负责（Responsible）、谁批准（Accountable）、谁被咨询（Consulted）以及谁被告知（Informed）。

| 流程阶段                        | 产品经理 (PM) | 研发工程师 (Dev) | 质量保证工程师 (QA) | 运维工程师 (Ops) |
| :------------------------------ | :------------ | :--------------- | :------------------ | :--------------- |
| **1\. 需求定义与排期**          | R/A           | C                | C                   | I                |
| **2\. 功能开发与单元测试**      | C             | R/A              | C                   | I                |
| **3\. 研发自测与报告准备**      | I             | R/A              | C                   | I                |
| **4\. 代码审查 (Code Review)**  | I             | R/A              | I                   | I                |
| **5\. 提交测试请求**            | I             | R                | A                   | I                |
| **6\. 测试用例设计**            | C             | C                | R/A                 | I                |
| **7\. 测试执行与缺陷报告**      | I             | C                | R/A                 | I                |
| **8\. 缺陷修复**                | I             | R/A              | C                   | I                |
| **9\. 回归测试**                | I             | C                | R/A                 | I                |
| **10\. QA签核与测试报告**       | I             | I                | R/A                 | C                |
| **11\. 发布排期与Go/No-Go决策** | C             | C                | C                   | R/A              |
| **12\. 生产环境部署**           | I             | C                | C                   | R/A              |
| **13\. 线上功能验证**           | C             | C                | R/A                 | C                |
| **14\. 发布后监控**             | I             | C                | C                   | R/A              |

---

## **第三部分：端到端工作流程**

本章节详细描述了从需求提出到功能上线的完整生命周期，共分为三个主要阶段：规划与开发、质量保证、发布与验证。

### **3.1 流程图 (Process Flow Diagram)**

以下流程图直观地展示了各角色（泳道）之间的协作关系和工作流转。

```mermaid
flowchart TD
    subgraph "产品管理 (PM)"
        A1[需求收集/产品规划] --> A2{需求评审}
        A2 --> A3[排期 & 创建Jira任务]
    end

    subgraph "研发 (Development)"
        B0[拟定技术方案] --> B0_1{技术方案审查}
        B0_1 -->|审查通过| B1[功能开发 & 单元测试]
        B0_1 -->|审查不通过| B0
        B1 --> B2[代码审查]
        B2 --> B3[执行自测 & 准备自测报告]
        B3 --> B4[准备回滚方案]
        B4 --> B5[提交测试请求]
        B6[BUG修复] --> B2
    end

    subgraph "质量保证 (QA)"
        C1[测试用例设计与评审] --> C2[接收测试请求 & DoR检查]
        C2 --> C3[测试环境验证]
        C3 --> C4[执行功能/集成测试]
        C4 --> C5{测试结果}
        C5 -->|发现BUG| C6[创建BUG票]
        C5 -->|测试通过| C7[执行回归测试]
        C6 --> B6
        C7 --> C8[输出测试报告 & QA签核]
    end

    subgraph "发布与运维 (Release & Ops)"
        D1[创建上线票 & 关联QA票] --> D2{发布前最终会议}
        D2 -->|Go决策| D3[执行生产发布]
        D2 -->|No-Go决策| D4[延期发布]
        D3 --> D5[生产环境验证]
        D5 --> D6([发布完成 & 启动监控])
        D4 --> B6
    end

    %% 主要流程连接
    A3 --> B0
    A3 --> C1
    B5 --> C2
    C2 -->|DoR通过| C3
    C2 -->|DoR驳回| B3
    C8 --> D1

    %% 样式类定义
    classDef pmStyle fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef devStyle fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef qaStyle fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef opsStyle fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef bugFix fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c
    classDef endNode fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32

    %% 应用样式类
    class A1,A2,A3 pmStyle
    class B0,B1,B2,B3,B4,B5 devStyle
    class C1,C2,C3,C4,C7,C8 qaStyle
    class D1,D3,D5 opsStyle
    class A2,B0_1,C5,D2 decision
    class B6,C6,D4 bugFix
    class D6 endNode
```

### **3.2 阶段一：规划、开发与预测试**

此阶段涵盖了从需求确认到正式提交测试前的所有研发活动。

1. **需求定义与排期 (PM)**：产品经理负责定义业务需求（PRD），组织需求评审会议，并在评审通过后，将需求拆解为可执行的任务，录入Jira系统并进行排期。  
2. **功能开发与单元测试 (Dev)**：研发工程师根据Jira任务进行编码实现，并编写充分的单元测试以保证代码模块级别的质量。  
3. **代码审查 (Dev)**：在提交测试前，代码必须经过至少一位同事的审查（Peer Review）。审查人需对代码质量、规范性、可维护性负责，其姓名和审查完成的记录必须在Jira任务中明确标注。  
4. **自测与产物准备 (Dev)**：研发工程师必须在本地或开发测试环境中对所开发的功能进行全面的自测，并依据测试结果编写《自测报告》。同时，必须编写或更新对应的《回滚手顺》。这两份文档是提交测试的必要条件。

### **3.3 阶段二：正式质量保证**

此阶段是QA团队主导的、系统性的质量验证过程。

1. **测试请求与DoR检查 (QA)**：研发工程师完成阶段一所有工作后，在Jira中将任务状态流转至QA。QA工程师接收到请求后，将首先根据《就绪定义 (DoR)》清单（详见4.2章节）进行检查。任何不满足DoR要求的提测都将被驳回，并附带明确的理由。  
2. **测试执行 (QA)**：在测试环境验证无误后，QA工程师依据测试用例，执行功能测试、集成测试、接口测试等。所有发现的缺陷都必须在Jira中创建BUG票，并清晰描述复现步骤、预期与实际结果，同时关联到对应的开发任务票。  
3. **缺陷修复与回归循环 (Dev & QA)**：研发工程师根据BUG票修复缺陷，完成后再次提交测试。QA工程师在验证缺陷已被修复的同时，需要执行相应的回归测试，以确保修复没有引入新的问题。此过程可能多次迭代，直至产品质量达到发布标准。  
4. **QA签核 (QA Sign-off)**：当所有主要功能通过测试，所有严重级别的缺陷都已关闭或经相关方确认可延后修复，且回归测试通过后，QA团队将出具正式的《测试报告》。该报告总结了测试范围、过程、结果和遗留风险。同时，QA会在Jira中对测试票进行“签核”（Sign-off），标志着该功能在质量上已具备发布条件。

### **3.4 阶段三：生产发布与验证**

此阶段是将通过测试的功能安全、可靠地部署到生产环境的过程。

1. **发布票创建与排期 (Ops/Release Manager)**：运维或发布负责人创建上线票，并必须在票中关联已经获得QA签核的测试票。随后，与产品、研发、QA团队协调，根据发布策略（见5.1章节）确定最终的发布时间窗口。  
2. **Go/No-Go决策会议 (All Stakeholders)**：对于所有重要产品或重大功能变更，在预定发布时间点前，必须召开一个简短的Go/No-Go会议。参会人员包括核心研发、QA、运维及产品代表。会议的目的是最后一次确认所有前置条件均已满足，并对发布风险达成共识。  
3. **生产部署 (Ops)**：部署操作严格遵循“职责分离”原则，由运维工程师（非功能研发人员）执行。运维人员需严格按照上线票中提供的部署步骤和回滚方案进行操作。如对流程有任何疑问，必须暂停发布，并立即与研发、QA沟通确认。  
4. **线上验证 (QA/Dev)**：部署完成后，QA或研发人员需要立即登录生产环境，对上线功能的核心路径进行一次快速的烟雾测试（Smoke Test），确保其基本可用。  
5. **发布完成与监控**：线上验证通过后，正式宣告发布完成。运维团队将启动对相关系统指标的重点监控，以确保服务在发布后的稳定性。

## **第四部分：工具与产物标准**

本章节旨在规范化流程中使用的工具和必须产出的文档，确保信息流转的标准化和高效性。

### **4.1 Jira治理策略**

Jira作为本流程的核心协作平台，其使用必须遵循以下强制性规范：

* **强制性票据层级与链接**：所有工作必须遵循 开发任务 → QA测试任务 → 上线任务 的严格链接结构。不允许存在孤立的、无法追溯源头的测试或上线票。这一结构是构建完整审计追踪链的基础。  
* **标准化的状态流转**：Jira工作流中的每一个状态（如“待办”、“开发中”、“待测试”、“已解决”、“已关闭”）都有明确的定义和准入/准出条件。例如，只有QA工程师有权限将票据状态从“测试中”变更为“待发布”。  
* **信息集中化**：所有与任务相关的信息，包括但不限于需求细节、讨论记录、代码diff链接、算法审核人及时间、确认记录等，都必须记录在Jira票据的描述或评论区中。这确保Jira成为该任务唯一的、权威的信息源（Single Source of Truth）。

### **4.2 “就绪定义” (Definition of Ready \- DoR) for QA**

“就绪定义”是QA团队接收测试请求时的一道正式质量门。它将提测方（研发）的责任和交付标准明确化，减少了因信息不全导致的沟通成本和返工。只有满足以下所有条件的测试请求，才被认为是“就绪”的。

| \#   | 检查项                           | 状态 | 备注                                     |
| :--- | :------------------------------- | :--- | :--------------------------------------- |
| 1    | 开发Jira任务已链接至本QA任务     | ☐    | 遵循 开发 → QA 的链接规则                |
| 2    | 代码已完成同行评审 (Code Review) | ☐    | 评审人姓名及完成记录已在开发票中注明     |
| 3    | 代码仓库的Diff链接已提供         | ☐    | 链接需填写在开发任务票中                 |
| 4    | 详尽的《自测报告》已附加或链接   | ☐    | 报告必须包含明确的自测时间               |
| 5    | 最终版的《回滚手顺》链接已提供   | ☐    | 如无回滚方案，需提供可接受的理由并获批准 |
| 6    | 目标测试业务与预期结果已清晰描述 | ☐    | 确保QA能理解并正确操作                   |
| 7    | 目标测试环境已明确指定           | ☐    | 如开发测试环境、生产环境等               |
| 8    | 已向QA进行简要的功能演示或讲解   | ☐    | 确保信息对齐，提高测试效率               |

## **第五部分：发布治理与策略**

本章节整合了所有与生产发布直接相关的规则和策略，为发布决策和执行提供清晰的指导。

### **5.1 发布节奏与时间表**

为平衡业务需求与系统风险，不同类型产品的发布需遵循不同的时间窗口策略。

| 产品类别       | 标准发布窗口     | 发布截止时间 (北京时间) | 部署人员要求   |
| :------------- | :--------------- | :---------------------- | :------------- |
| **重要产品**   | 需与各方协调安排 | **17:30**               | 严格的职责分离 |
| **非重要产品** | 工作时间内皆可   | 与QA协调即可            | 严格的职责分离 |

任何需要在标准时间之外进行的发布，均被视为特殊情况，必须提前与所有相关方（尤其是QA和运维）沟通协调，并获得批准。

### **5.2 非周期性与紧急发布协议**

* **定义**：紧急发布专用于处理对业务构成严重威胁的突发事件，例如：生产环境服务宕机、严重安全漏洞、导致核心功能不可用的关键缺陷等。  
* **流程**：紧急发布可绕过部分标准流程（如完整的回归测试），但必须遵循一个加速的审批和执行流程。  
  1. **发起**：由研发或运维负责人发起，并立即创建紧急Jira票。  
  2. **评估**：由技术负责人、核心研发和QA负责人共同快速评估变更的必要性、风险和影响。  
  3. **审批**：必须获得技术负责人（或CTO）的明确批准。  
  4. **执行**：部署仍需遵循“职责分离”原则，并有详细的记录。  
  5. **事后**：发布后需进行完整的复盘，并补全相关文档。

### **5.3 “双人原则”：研发与部署的职责分离**

本SOP强制要求，执行生产环境部署的人员不能是待发布功能的主要研发人员。这一原则，也称为“双人原则”或“四眼原则”，其背后的风险管理逻辑在于：

1. **降低操作失误风险**：由一个不熟悉代码细节但熟悉部署流程的人来执行操作，更能严格遵循部署文档，一双“新鲜的眼睛”更容易发现文档或流程中的疏漏。  
2. **防止未经授权的变更**：杜绝了研发人员在最后时刻绕过测试和审查，直接部署未经审核的代码的可能。  
3. **促进文档质量提升**：研发人员必须编写出清晰、完整、他人能够独立理解和执行的部署与回滚文档，从而提升了知识传递的质量和团队的韧性。

如果上线人员对部署内容或流程有任何疑议，必须暂停发布，并召开由QA、研发、上线人员共同参与的沟通会议。只有在所有疑虑都得到解决，并由技术负责人确认风险可控后，方可继续发布。

## **第六部分：附录**

### **6.1 流程例外：研发主导的自发布协议**

虽然标准流程要求所有变更都需经过QA测试，但对于某些特定类型的变更，允许研发团队在自测后直接发布。此例外旨在提高特定场景下的迭代效率，但必须在严格的框架内进行，以控制风险。

* **适用场景**：此协议仅适用于那些不涉及API变更、不改变核心业务逻辑、不影响用户界面的“精度相关功能点”。典型例子包括：  
  * 机器学习模型权重文件的更新。  
  * 不改变输入输出结构的算法参数调优。  
  * 数据ETL脚本中与数据质量相关的逻辑微调。  
* **审批流程**：  
  1. 研发人员必须在Jira中创建专门的“自发布”类型任务。  
  2. 在任务描述中，必须详细说明变更内容、自测方案、评估指标以及为何适用于此例外协议。  
  3. 变更必须获得直属技术负责人（Tech Lead）和对应产品经理（PM）的共同批准，批准记录需留在Jira评论中。  
* **最低要求**：即使豁免了QA的完整测试，也必须满足以下最低质量保证要求：  
  1. **详尽的自测报告**：报告需重点展示变更前后的关键指标对比（如模型准确率、召回率等）。  
  2. **同行评审 (Peer Review)**：代码或配置变更必须由另一位具备相应能力的同事进行评审。  
  3. **完备的回滚方案**：必须提供并验证过可以快速回滚此次变更的方案。  
  4. **完整的Jira记录**：所有操作、决策和产出物都必须在Jira任务中记录，以备审计。

### **6.2 升级路径 (Escalation Paths)**

在流程执行过程中，如遇分歧或障碍，应遵循以下路径进行沟通和决策升级，以确保问题得到高效、公正的解决。

* **关于“就绪定义(DoR)”的争议**：  
  * 第一层：当事研发人员与QA工程师。  
  * 升级至：QA负责人。  
* **关于缺陷优先级的争议**：  
  * 第一层：当事研发人员、QA工程师与产品经理。  
  * 升级至：研发负责人与产品负责人。  
* **关于发布时间或Go/No-Go决策的争议**：  
  * 第一层：核心研发、QA、运维及产品代表。  
  * 升级至：技术负责人或工程总监。  
* **关于本SOP流程本身的解释或适用性的争议**：  
  * 升级至：流程制定与维护团队（如技术管理委员会）。
