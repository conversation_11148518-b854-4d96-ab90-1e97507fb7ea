# Terraform Infrastructure Management Workflow

## 项目概述

Terraform Infrastructure Management 是一个基于 Terraform 的云基础设施管理平台，专注于 AWS EC2 实例的状态控制和管理。该项目提供了模块化、可重用的基础设施组件，支持多种云环境的集成。

## 工作流程图

```mermaid
flowchart TD
    Start([开始 Terraform 操作]) --> CheckPrereq{检查先决条件}
    
    %% 先决条件检查
    CheckPrereq -->|缺少工具| InstallTools[安装必要工具<br/>- Terraform CLI<br/>- AWS CLI]
    CheckPrereq -->|工具已安装| CheckCreds{检查 AWS 凭证}
    InstallTools --> CheckCreds
    
    %% AWS 凭证配置
    CheckCreds -->|未配置| ConfigCreds[配置 AWS 凭证]
    CheckCreds -->|已配置| SelectProject{选择项目}
    
    ConfigCreds --> CredMethod{选择凭证方法}
    CredMethod -->|环境变量| SetEnvVars[设置环境变量<br/>AWS_ACCESS_KEY_ID<br/>AWS_SECRET_ACCESS_KEY<br/>AWS_DEFAULT_REGION]
    CredMethod -->|共享凭证文件| SetCredFile[配置 ~/.aws/credentials<br/>设置 default profile]
    CredMethod -->|IAM 角色| SetIAMRole[配置 IAM 角色<br/>适用于 EC2 环境]
    
    SetEnvVars --> SelectProject
    SetCredFile --> SelectProject
    SetIAMRole --> SelectProject
    
    %% 项目选择
    SelectProject -->|aws/windows| WindowsProject[AWS Windows EC2 管理项目]
    SelectProject -->|其他项目| OtherProject[其他云资源项目<br/>（待扩展）]
    
    %% Windows EC2 项目流程
    WindowsProject --> NavToDir[导航到项目目录<br/>cd terraform/aws/windows]
    NavToDir --> TerraformInit[初始化 Terraform<br/>terraform init<br/>下载 provider 插件]
    TerraformInit --> ConfigVars{配置变量}
    
    %% 变量配置
    ConfigVars -->|命令行方式| CmdLineVars[使用命令行参数<br/>-var=region=us-east-1<br/>-var=instance_id=i-xxx<br/>-var=action=start/stop]
    ConfigVars -->|文件方式| TfvarsFile[使用 terraform.tfvars<br/>复制 terraform.tfvars.example<br/>编辑实际值]
    
    %% 变量验证
    CmdLineVars --> ValidateVars[验证变量]
    TfvarsFile --> ValidateVars
    ValidateVars --> VarCheck{变量验证}
    VarCheck -->|验证失败| VarError[显示验证错误<br/>- region 格式错误<br/>- instance_id 格式错误<br/>- action 值无效]
    VarCheck -->|验证成功| TerraformPlan[生成执行计划<br/>terraform plan]
    
    VarError --> ConfigVars
    
    %% 执行计划和应用
    TerraformPlan --> ReviewPlan{审查执行计划}
    ReviewPlan -->|计划有误| ModifyConfig[修改配置]
    ReviewPlan -->|计划正确| TerraformApply[应用配置<br/>terraform apply]
    ModifyConfig --> TerraformPlan
    
    %% 实例操作执行
    TerraformApply --> ActionType{操作类型}
    ActionType -->|action=start| StartInstance[启动 EC2 实例]
    ActionType -->|action=stop| StopInstance[停止 EC2 实例]
    
    %% 启动实例流程
    StartInstance --> StartCmd[执行 AWS CLI 命令<br/>aws ec2 start-instances]
    StartCmd --> WaitRunning[等待实例运行<br/>aws ec2 wait instance-running]
    WaitRunning --> GetIP[获取公网 IP<br/>aws ec2 describe-instances]
    GetIP --> SaveIP[保存 IP 到文件<br/>instance_ip.txt]
    SaveIP --> OutputIP[输出实例 IP<br/>terraform output]
    
    %% 停止实例流程
    StopInstance --> StopCmd[执行 AWS CLI 命令<br/>aws ec2 stop-instances]
    StopCmd --> WaitStopped[等待实例停止<br/>aws ec2 wait instance-stopped]
    WaitStopped --> ClearIP[清空 IP 文件<br/>instance_ip.txt]
    ClearIP --> OutputEmpty[输出空 IP 值]
    
    %% 结果处理
    OutputIP --> Success[操作成功完成]
    OutputEmpty --> Success
    Success --> Cleanup{是否清理资源}
    Cleanup -->|是| TerraformDestroy[销毁资源<br/>terraform destroy]
    Cleanup -->|否| Maintain[保持当前状态]
    
    TerraformDestroy --> End([操作完成])
    Maintain --> End
    
    %% 其他项目分支（占位符）
    OtherProject --> FutureFeature[未来功能<br/>其他云服务支持]
    FutureFeature --> End
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef aws fill:#ff9800,stroke:#e65100,stroke-width:2px,color:#fff
    classDef config fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    
    class Start,End startEnd
    class InstallTools,NavToDir,TerraformInit,TerraformPlan,TerraformApply,StartCmd,WaitRunning,GetIP,SaveIP,StopCmd,WaitStopped,ClearIP,TerraformDestroy process
    class CheckPrereq,CheckCreds,CredMethod,SelectProject,ConfigVars,VarCheck,ReviewPlan,ActionType,Cleanup decision
    class VarError error
    class StartInstance,StopInstance,OutputIP,OutputEmpty aws
    class SetEnvVars,SetCredFile,SetIAMRole,CmdLineVars,TfvarsFile,ValidateVars config
```

## 主要组件说明

### 核心功能
- **EC2 实例状态管理**: 启动和停止现有的 AWS EC2 实例
- **IP 地址管理**: 自动获取和管理实例的公网 IP 地址
- **状态跟踪**: 通过 Terraform state 文件跟踪资源状态

### 项目结构
- **terraform/aws/windows/**: AWS Windows EC2 管理模块
- **main.tf**: 核心 Terraform 配置文件
- **terraform.tfvars.example**: 变量配置示例文件
- **instance_ip.txt**: 实例 IP 地址输出文件

### 变量配置
- **region**: AWS 区域（如 us-east-1）
- **instance_id**: EC2 实例 ID（如 i-1234567890abcdef0）
- **action**: 操作类型（start 或 stop）

## 使用示例

### 基本操作流程
```bash
# 1. 导航到项目目录
cd terraform/aws/windows

# 2. 初始化 Terraform
terraform init

# 3. 启动实例（命令行方式）
terraform apply -var="region=us-east-1" -var="instance_id=i-1234567890abcdef0" -var="action=start"

# 4. 停止实例（命令行方式）
terraform apply -var="region=us-east-1" -var="instance_id=i-1234567890abcdef0" -var="action=stop"
```

### 使用配置文件
```bash
# 1. 复制示例配置文件
cp terraform.tfvars.example terraform.tfvars

# 2. 编辑配置文件
# region      = "us-east-1"
# instance_id = "i-your_actual_instance_id"
# action      = "start"

# 3. 应用配置
terraform apply
```

## 关键特性

- **模块化设计**: 可重用的 Terraform 模块结构
- **变量验证**: 内置的输入验证确保配置正确性
- **状态管理**: 自动跟踪和管理基础设施状态
- **多种凭证方式**: 支持环境变量、共享文件、IAM 角色等认证方式
- **错误处理**: 完善的错误检查和处理机制
- **输出管理**: 自动获取和输出实例信息
