# Medical Backend Infrastructure Workflow

## 项目概述

Medical Backend Infrastructure Playbook 是一个 Ansible 框架，用于管理医疗后端服务，包括脚本执行、监控服务、数据库操作和服务重启功能。

## 工作流程图

```mermaid
flowchart TD
    Start([开始执行 Medical Backend Playbook]) --> ValidateParams{验证 target_group 参数}
    
    ValidateParams -->|参数无效| Error1[显示错误信息<br/>列出可用的组]
    ValidateParams -->|参数有效| SetupDeps[设置依赖角色]
    
    Error1 --> End1([执行失败])
    
    SetupDeps --> SetFacts[Dep.SetFacts<br/>设置全局变量和配置]
    SetFacts --> SshConn[Dep.SshConnection<br/>配置SSH连接]
    SshConn --> CommonRole[01-common<br/>通用任务处理]
    
    CommonRole --> ScriptCheck{检查是否需要执行脚本}
    ScriptCheck -->|script_name 已定义| CopyBaseScripts[复制基础脚本到远程主机]
    ScriptCheck -->|script_name 未定义| SkipScript[跳过脚本执行]
    
    CopyBaseScripts --> CopyMainScript[复制主脚本到远程主机]
    CopyMainScript --> ExecuteScript[执行脚本<br/>包含超时和错误处理]
    ExecuteScript --> LogResults[记录执行结果和日志]
    
    LogResults --> CheckOtherTasks{检查其他任务标签}
    SkipScript --> CheckOtherTasks
    
    CheckOtherTasks -->|grafana 标签| GrafanaRole[02-grafana<br/>监控服务管理]
    CheckOtherTasks -->|database 标签| DatabaseRole[03-database<br/>数据库操作]
    CheckOtherTasks -->|restart_services 标签| RestartRole[04-restart-services<br/>服务重启]
    CheckOtherTasks -->|无其他标签| Complete[任务完成]
    
    %% Grafana 分支
    GrafanaRole --> GrafanaChoice{选择 Grafana 操作}
    GrafanaChoice -->|upgrade_services| UpgradeServices[升级监控服务<br/>同步 docker-compose.yml<br/>更新服务版本]
    GrafanaChoice -->|update_configs| UpdateConfigs[更新配置文件<br/>node-exporter.yml<br/>mongodb-exporter.yml<br/>redis-monitor.conf<br/>loki-config.yaml<br/>promtail-config.yaml]
    
    UpgradeServices --> RestartMonitoring[重启相关监控服务]
    UpdateConfigs --> RestartMonitoring
    RestartMonitoring --> GrafanaComplete[Grafana 任务完成]
    
    %% Database 分支
    DatabaseRole --> DatabaseOps[执行数据库操作<br/>MongoDB 认证更新<br/>字符串替换操作]
    DatabaseOps --> DatabaseComplete[数据库任务完成]
    
    %% Restart Services 分支
    RestartRole --> ParseGroups[解析 restart_groups 变量]
    ParseGroups --> ParseServices[解析 restart_services 变量]
    ParseServices --> LoadServiceInfo[加载服务配置信息]
    LoadServiceInfo --> CollectHosts[收集目标主机列表]
    CollectHosts --> CreateTargetGroup[创建 restart_targets 组]
    CreateTargetGroup --> ExecuteRestart[执行服务重启操作]
    ExecuteRestart --> RestartComplete[服务重启完成]
    
    %% 汇聚点
    GrafanaComplete --> Complete
    DatabaseComplete --> Complete
    RestartComplete --> Complete
    Complete --> End2([执行成功])
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef role fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    
    class Start,End1,End2 startEnd
    class SetFacts,SshConn,CopyBaseScripts,CopyMainScript,ExecuteScript,LogResults,UpgradeServices,UpdateConfigs,RestartMonitoring,DatabaseOps,ParseGroups,ParseServices,LoadServiceInfo,CollectHosts,CreateTargetGroup,ExecuteRestart process
    class ValidateParams,ScriptCheck,CheckOtherTasks,GrafanaChoice decision
    class Error1 error
    class CommonRole,GrafanaRole,DatabaseRole,RestartRole role
```

## 主要组件说明

### 依赖角色 (Dependencies)
- **Dep.SetFacts**: 设置全局变量和配置，包括路径、超时设置等
- **Dep.SshConnection**: 管理SSH连接配置和known_hosts
- **Dep.RepoOps**: 处理Git仓库操作和同步

### 核心角色 (Core Roles)
- **01-common**: 通用任务处理，主要负责脚本执行
- **02-grafana**: 监控服务管理，包括服务升级和配置更新
- **03-database**: 数据库操作，主要是MongoDB相关操作
- **04-restart-services**: 服务重启管理，支持组和单个服务重启

## 使用示例

### 基本脚本执行
```bash
ansible-playbook site.yml -e "script_name=health_check.sh target_group=medical_servers"
```

### 监控服务升级
```bash
ansible-playbook site.yml --tags grafana,upgrade_services
```

### 服务重启
```bash
ansible-playbook site.yml --tags restart_services -e "restart_groups=['backend_group']"
```

### 数据库操作
```bash
ansible-playbook site.yml --tags database -e "@extra-vars/database.yml"
```

## 关键特性

- **健壮的脚本执行**: 包含错误处理、超时管理和适当的退出码管理
- **幂等操作**: 可安全地多次运行，结果一致
- **全面的日志记录**: 详细的执行日志，包含时间戳和状态跟踪
- **健康监控**: 内置健康检查和资源监控
- **灵活的目标定位**: 可在特定的清单组上执行脚本
- **备份管理**: 自动备份和清理旧的执行日志
